import * as fs from "fs/promises"
import * as path from "path"
import { BaseAgentTool } from "../base-agent.tool"
import { MoveFileToolParams } from "../schema/move_file"
import { getReadablePath } from "../../utils"

export class MoveFileTool extends BaseAgentTool<MoveFileToolParams> {
	async execute() {
		const { input } = this.params
		const { source, destination, overwrite = false, dry_run = false } = input
		
		// Validate required parameters
		if (!source || typeof source !== 'string' || source.trim() === '') {
			const errorMsg = `
			<move_file_response>
				<status>
					<r>error</r>
					<operation>move_file</operation>
					<timestamp>${new Date().toISOString()}</timestamp>
				</status>
				<error_details>
					<type>invalid_parameter</type>
					<message>Source path is required and must be a non-empty string</message>
					<provided_source>${String(source || 'null')}</provided_source>
				</error_details>
			</move_file_response>`
			return this.toolResponse("error", errorMsg)
		}

		if (!destination || typeof destination !== 'string' || destination.trim() === '') {
			const errorMsg = `
			<move_file_response>
				<status>
					<r>error</r>
					<operation>move_file</operation>
					<timestamp>${new Date().toISOString()}</timestamp>
				</status>
				<error_details>
					<type>invalid_parameter</type>
					<message>Destination path is required and must be a non-empty string</message>
					<provided_destination>${String(destination || 'null')}</provided_destination>
				</error_details>
			</move_file_response>`
			return this.toolResponse("error", errorMsg)
		}

		try {
			const absoluteSource = path.resolve(this.cwd, source)
			const absoluteDestination = path.resolve(this.cwd, destination)

			// Check if source exists
			try {
				await fs.access(absoluteSource)
			} catch {
				const errorMsg = `
				<move_file_response>
					<status>
						<r>error</r>
						<operation>move_file</operation>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<error_details>
						<type>file_not_found</type>
						<message>Source file or directory does not exist</message>
						<source>${getReadablePath(source, this.cwd)}</source>
					</error_details>
				</move_file_response>`
				return this.toolResponse("error", errorMsg)
			}

			// Check if destination exists and handle overwrite
			let destinationExists = false
			try {
				await fs.access(absoluteDestination)
				destinationExists = true
			} catch {
				// Destination doesn't exist, which is fine
			}

			if (destinationExists && !overwrite) {
				const errorMsg = `
				<move_file_response>
					<status>
						<r>error</r>
						<operation>move_file</operation>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<error_details>
						<type>destination_exists</type>
						<message>Destination already exists and overwrite is not enabled</message>
						<source>${getReadablePath(source, this.cwd)}</source>
						<destination>${getReadablePath(destination, this.cwd)}</destination>
					</error_details>
				</move_file_response>`
				return this.toolResponse("error", errorMsg)
			}

			// Determine if this is a rename (same directory) or move
			const sourceDir = path.dirname(absoluteSource)
			const destDir = path.dirname(absoluteDestination)
			const isRename = sourceDir === destDir

			if (!dry_run) {
				// Ensure destination directory exists
				const destinationDir = path.dirname(absoluteDestination)
				await fs.mkdir(destinationDir, { recursive: true })

				// Perform the move operation
				await fs.rename(absoluteSource, absoluteDestination)
			}

			// Success response
			return this.toolResponse(
				"success",
				`<move_file_response>
					<status>
						<r>success</r>
						<operation>move_file</operation>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<details>
						<source>${getReadablePath(source, this.cwd)}</source>
						<destination>${getReadablePath(destination, this.cwd)}</destination>
						<type>${isRename ? 'rename' : 'move'}</type>
						<dry_run>${dry_run}</dry_run>
						<overwrite_enabled>${overwrite}</overwrite_enabled>
						<message>${dry_run ? 'Preview: ' : ''}File ${isRename ? 'renamed' : 'moved'} successfully</message>
					</details>
				</move_file_response>`
			)

		} catch (error) {
			const errorMsg = `
			<move_file_response>
				<status>
					<r>error</r>
					<operation>move_file</operation>
					<timestamp>${new Date().toISOString()}</timestamp>
				</status>
				<error_details>
					<type>operation_failed</type>
					<message>${error instanceof Error ? error.message : 'Unknown error occurred'}</message>
					<source>${getReadablePath(source, this.cwd)}</source>
					<destination>${getReadablePath(destination, this.cwd)}</destination>
				</error_details>
			</move_file_response>`
			return this.toolResponse("error", errorMsg)
		}
	}
}