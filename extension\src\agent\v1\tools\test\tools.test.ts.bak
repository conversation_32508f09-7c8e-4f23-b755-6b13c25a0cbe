import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import * as fs from 'fs/promises'
import * as path from 'path'
import { UndoTool } from '../runners/undo.tool'
import { MoveFileTool } from '../runners/move-file.tool'
import { RenameFileTool } from '../runners/rename-file.tool'
import { DeleteFileTool } from '../runners/delete-file.tool'
import { LearningTool } from '../runners/learning.tool'
import { BaseAgentTool } from '../base-agent.tool'

// Mock dependencies
vi.mock('fs/promises')
vi.mock('serialize-error')

const mockFs = vi.mocked(fs)

// Mock MainAgent and StateManager
const mockStateManager = {
    getFilesInTaskDirectory: vi.fn(),
    getFileVersions: vi.fn(),
    alwaysAllowReadOnly: false,
    alwaysAllowWriteOnly: false,
    subAgentManager: { state: null }
}

const mockMainAgent = {
    getStateManager: () => mockStateManager,
    taskExecutor: {
        ask: vi.fn(),
        say: vi.fn(),
        updateAsk: vi.fn()
    }
}

const mockOptions = {
    cwd: '/test',
    alwaysAllowReadOnly: false,
    alwaysAllowWriteOnly: false,
    MainAgent: mockMainAgent,
    setRunningProcessId: vi.fn(),
    agentName: undefined
}

const createMockParams = (name: string, input: any) => ({
    name,
    input,
    id: 'test-id',
    ts: Date.now(),
    isFinal: true,
    isLastWriteToFile: false,
    ask: vi.fn(),
    say: vi.fn(),
    updateAsk: vi.fn()
})

describe('Tool Tests', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    afterEach(() => {
        vi.restoreAllMocks()
    })

    describe('UndoTool', () => {
        it('should handle no operations to undo', async () => {
            mockStateManager.getFilesInTaskDirectory.mockResolvedValue({})

            const params = createMockParams('undo', { steps: 1 })
            const tool = new UndoTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('no_operations_to_undo')
        })

        it('should handle undo with reason', async () => {
            mockStateManager.getFilesInTaskDirectory.mockResolvedValue({
                'test.txt': { versions: ['v1', 'v2'] }
            })
            mockStateManager.getFileVersions.mockResolvedValue(['v1', 'v2'])

            const params = createMockParams('undo', {
                steps: 1,
                reason: 'Test undo reason'
            })
            const tool = new UndoTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Test undo reason')
        })

        it('should handle multiple steps undo', async () => {
            mockStateManager.getFilesInTaskDirectory.mockResolvedValue({
                'test1.txt': { versions: ['v1', 'v2'] },
                'test2.txt': { versions: ['v1', 'v2'] }
            })
            mockStateManager.getFileVersions.mockResolvedValue(['v1', 'v2'])

            const params = createMockParams('undo', { steps: 2 })
            const tool = new UndoTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('<steps>2</steps>')
        })
    })

    describe('MoveFileTool', () => {
        it('should validate required parameters', async () => {
            const params = createMockParams('move_file', {})
            const tool = new MoveFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('missing_parameter')
        })

        it('should handle source file not found', async () => {
            mockFs.access.mockRejectedValue(new Error('File not found'))

            const params = createMockParams('move_file', {
                source: 'nonexistent.txt',
                destination: 'dest.txt'
            })
            const tool = new MoveFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('source_not_found')
        })

        it('should successfully move file', async () => {
            mockFs.access.mockResolvedValueOnce(undefined) // source exists
            mockFs.access.mockRejectedValueOnce(new Error('Dest not found')) // dest doesn't exist
            mockFs.mkdir.mockResolvedValue(undefined)
            mockFs.rename.mockResolvedValue(undefined)

            const params = createMockParams('move_file', {
                source: 'source.txt',
                destination: 'dest.txt'
            })
            const tool = new MoveFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('File moved successfully')
        })

        it('should handle destination exists without overwrite', async () => {
            mockFs.access.mockResolvedValue(undefined) // both source and dest exist

            const params = createMockParams('move_file', {
                source: 'source.txt',
                destination: 'dest.txt',
                overwrite: false
            })
            const tool = new MoveFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('destination_exists')
        })
    })

    describe('RenameFileTool', () => {
        it('should validate required parameters', async () => {
            const params = createMockParams('rename_file', {})
            const tool = new RenameFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('missing_parameter')
        })

        it('should handle source file not found', async () => {
            mockFs.access.mockRejectedValue(new Error('File not found'))

            const params = createMockParams('rename_file', {
                path: 'nonexistent.txt',
                new_name: 'newname.txt'
            })
            const tool = new RenameFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('source_not_found')
        })

        it('should successfully rename file', async () => {
            mockFs.access.mockResolvedValueOnce(undefined) // source exists
            mockFs.access.mockRejectedValueOnce(new Error('New name not found')) // new name doesn't exist
            mockFs.rename.mockResolvedValue(undefined)

            const params = createMockParams('rename_file', {
                path: 'oldname.txt',
                new_name: 'newname.txt'
            })
            const tool = new RenameFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('File renamed successfully')
        })

        it('should handle destination name already exists', async () => {
            mockFs.access.mockResolvedValue(undefined) // both old and new names exist

            const params = createMockParams('rename_file', {
                path: 'oldname.txt',
                new_name: 'existingname.txt'
            })
            const tool = new RenameFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('destination_exists')
        })
    })

    describe('DeleteFileTool', () => {
        it('should validate required parameters', async () => {
            const params = createMockParams('delete_file', {})
            const tool = new DeleteFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('missing_parameter')
        })

        it('should handle file not found', async () => {
            mockFs.stat.mockRejectedValue(new Error('File not found'))

            const params = createMockParams('delete_file', {
                path: 'nonexistent.txt'
            })
            const tool = new DeleteFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('not_found')
        })

        it('should successfully delete file', async () => {
            mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any)
            mockFs.unlink.mockResolvedValue(undefined)

            const params = createMockParams('delete_file', {
                path: 'test.txt'
            })
            const tool = new DeleteFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('File deleted successfully')
        })

        it('should handle directory deletion with force', async () => {
            mockFs.stat.mockResolvedValue({ isDirectory: () => true } as any)
            mockFs.readdir.mockResolvedValue(['file1.txt', 'file2.txt'] as any)
            mockFs.rmdir.mockResolvedValue(undefined)

            const params = createMockParams('delete_file', {
                path: 'testdir',
                force: true
            })
            const tool = new DeleteFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Directory deleted successfully')
        })

        it('should handle non-empty directory without force', async () => {
            mockFs.stat.mockResolvedValue({ isDirectory: () => true } as any)
            mockFs.readdir.mockResolvedValue(['file1.txt', 'file2.txt'] as any)

            const params = createMockParams('delete_file', {
                path: 'testdir',
                force: false
            })
            const tool = new DeleteFileTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('error')
            expect(result.text).toContain('directory_not_empty')
        })
    })

    describe('LearningTool', () => {
        it('should handle analyze action', async () => {
            const params = createMockParams('learning', {
                action: 'analyze',
                chatContext: 'Test context',
                problemType: 'test_problem',
                successOutcome: true
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Analyzed chat context')
            expect(result.text).toContain('test_problem')
        })

        it('should handle create_skill action', async () => {
            const params = createMockParams('learning', {
                action: 'create_skill',
                skillName: 'test_skill',
                skillData: { description: 'Test skill' }
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Created new skill: test_skill')
        })

        it('should handle search_skill action', async () => {
            const params = createMockParams('learning', {
                action: 'search_skill',
                searchQuery: 'test query',
                problemType: 'search_problem'
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Searched for skills matching: test query')
        })

        it('should handle incremental_update action', async () => {
            const params = createMockParams('learning', {
                action: 'incremental_update',
                skillName: 'existing_skill',
                newExperience: { newCase: 'test case' }
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Incrementally updated skill: existing_skill')
        })

        it('should handle auto_evolve action with rollback', async () => {
            const params = createMockParams('learning', {
                action: 'auto_evolve',
                skillName: 'evolving_skill',
                rollbackOnFailure: true,
                successOutcome: false
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Auto-evolved skill: evolving_skill')
            expect(result.text).toContain('rollbackPerformed": true')
        })

        it('should handle form_intuition action', async () => {
            const params = createMockParams('learning', {
                action: 'form_intuition',
                skillName: 'intuitive_skill',
                intuitionTriggers: ['trigger1', 'trigger2'],
                intuitionLevel: 85
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Formed intuition for skill: intuitive_skill')
        })

        it('should handle compose_skills action', async () => {
            const params = createMockParams('learning', {
                action: 'compose_skills',
                skillCombination: ['skill1', 'skill2', 'skill3'],
                complexProblem: 'Complex multi-step problem'
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Composed skills for complex problem')
        })

        it('should handle decompose_complex_problem action', async () => {
            const params = createMockParams('learning', {
                action: 'decompose_complex_problem',
                complexProblem: 'Very complex system design problem',
                skillCombination: ['design_skill', 'implementation_skill']
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Decomposed complex problem into manageable skills')
        })

        it('should handle unknown action', async () => {
            const params = createMockParams('learning', {
                action: 'unknown_action' as any
            })
            const tool = new LearningTool(params, mockOptions)

            const result = await tool.execute()

            expect(result.status).toBe('success')
            expect(result.text).toContain('Unknown learning action: unknown_action')
        })
    })
})