/**
 * @fileoverview Git Repository Manager - Handles git operations for sub-agent system
 */

import * as fs from "fs/promises"
import * as path from "path"
import { execSync } from "child_process"

export interface GitStatus {
	isRepository: boolean
	hasUncommittedChanges: boolean
	currentBranch: string
	remoteUrl?: string
	lastCommit?: {
		hash: string
		message: string
		author: string
		date: string
	}
}

export interface GitSnapshot {
	id: string
	agentId: string
	timestamp: number
	branch: string
	commitHash: string
	changedFiles: string[]
	description: string
}

export class GitRepositoryManager {
	private cwd: string
	private snapshots: Map<string, GitSnapshot> = new Map()

	constructor(cwd: string) {
		this.cwd = cwd
	}

	/**
	 * Check if current directory is a git repository
	 */
	public async isGitRepository(): Promise<boolean> {
		try {
			const gitDir = path.join(this.cwd, ".git")
			const stats = await fs.stat(gitDir)
			return stats.isDirectory()
		} catch {
			return false
		}
	}

	/**
	 * Initialize git repository
	 */
	public async initializeRepository(): Promise<void> {
		try {
			execSync("git init", { cwd: this.cwd, stdio: "pipe" })
			
			// Create initial commit if no commits exist
			const hasCommits = await this.hasCommits()
			if (!hasCommits) {
				// Create .gitignore if it doesn't exist
				const gitignorePath = path.join(this.cwd, ".gitignore")
				try {
					await fs.access(gitignorePath)
				} catch {
					await fs.writeFile(gitignorePath, `# Sub-agent system files
.Ai/
node_modules/
*.log
.env
.DS_Store
`)
				}

				execSync("git add .", { cwd: this.cwd, stdio: "pipe" })
				execSync('git commit -m "Initial commit - Sub-agent system setup"', { 
					cwd: this.cwd, 
					stdio: "pipe" 
				})
			}
		} catch (error) {
			throw new Error(`Failed to initialize git repository: ${error instanceof Error ? error.message : String(error)}`)
		}
	}

	/**
	 * Get git status
	 */
	public async getGitStatus(): Promise<GitStatus> {
		const isRepo = await this.isGitRepository()
		
		if (!isRepo) {
			return {
				isRepository: false,
				hasUncommittedChanges: false,
				currentBranch: ""
			}
		}

		try {
			const currentBranch = execSync("git branch --show-current", { 
				cwd: this.cwd, 
				encoding: "utf8" 
			}).trim()

			const statusOutput = execSync("git status --porcelain", { 
				cwd: this.cwd, 
				encoding: "utf8" 
			})
			const hasUncommittedChanges = statusOutput.trim().length > 0

			let remoteUrl: string | undefined
			try {
				remoteUrl = execSync("git remote get-url origin", { 
					cwd: this.cwd, 
					encoding: "utf8" 
				}).trim()
			} catch {
				// No remote configured
			}

			let lastCommit: GitStatus["lastCommit"]
			try {
				const commitInfo = execSync('git log -1 --format="%H|%s|%an|%ad"', { 
					cwd: this.cwd, 
					encoding: "utf8" 
				}).trim()
				const [hash, message, author, date] = commitInfo.split("|")
				lastCommit = { hash, message, author, date }
			} catch {
				// No commits yet
			}

			return {
				isRepository: true,
				hasUncommittedChanges,
				currentBranch,
				remoteUrl,
				lastCommit
			}
		} catch (error) {
			throw new Error(`Failed to get git status: ${error instanceof Error ? error.message : String(error)}`)
		}
	}

	/**
	 * Create snapshot before sub-agent execution
	 */
	public async createSnapshot(agentId: string, description: string): Promise<string> {
		const status = await this.getGitStatus()
		if (!status.isRepository) {
			throw new Error("Not a git repository")
		}

		// Commit current changes if any
		if (status.hasUncommittedChanges) {
			execSync("git add .", { cwd: this.cwd, stdio: "pipe" })
			execSync(`git commit -m "Pre-agent snapshot: ${description}"`, { 
				cwd: this.cwd, 
				stdio: "pipe" 
			})
		}

		const commitHash = execSync("git rev-parse HEAD", { 
			cwd: this.cwd, 
			encoding: "utf8" 
		}).trim()

		const snapshotId = `snapshot_${agentId}_${Date.now()}`
		const snapshot: GitSnapshot = {
			id: snapshotId,
			agentId,
			timestamp: Date.now(),
			branch: status.currentBranch,
			commitHash,
			changedFiles: [],
			description
		}

		this.snapshots.set(snapshotId, snapshot)
		return snapshotId
	}

	/**
	 * Revert to snapshot (rollback changes)
	 */
	public async revertToSnapshot(snapshotId: string): Promise<void> {
		const snapshot = this.snapshots.get(snapshotId)
		if (!snapshot) {
			throw new Error(`Snapshot ${snapshotId} not found`)
		}

		try {
			// Reset to the snapshot commit
			execSync(`git reset --hard ${snapshot.commitHash}`, { 
				cwd: this.cwd, 
				stdio: "pipe" 
			})

			// Clean untracked files
			execSync("git clean -fd", { 
				cwd: this.cwd, 
				stdio: "pipe" 
			})

			console.log(`Reverted to snapshot ${snapshotId} (${snapshot.commitHash})`)
		} catch (error) {
			throw new Error(`Failed to revert to snapshot: ${error instanceof Error ? error.message : String(error)}`)
		}
	}

	/**
	 * Get diff since snapshot
	 */
	public async getDiffSinceSnapshot(snapshotId: string): Promise<string> {
		const snapshot = this.snapshots.get(snapshotId)
		if (!snapshot) {
			throw new Error(`Snapshot ${snapshotId} not found`)
		}

		try {
			const diff = execSync(`git diff ${snapshot.commitHash}..HEAD`, { 
				cwd: this.cwd, 
				encoding: "utf8" 
			})
			return diff
		} catch (error) {
			throw new Error(`Failed to get diff: ${error instanceof Error ? error.message : String(error)}`)
		}
	}

	/**
	 * Get changed files since snapshot
	 */
	public async getChangedFilesSinceSnapshot(snapshotId: string): Promise<string[]> {
		const snapshot = this.snapshots.get(snapshotId)
		if (!snapshot) {
			throw new Error(`Snapshot ${snapshotId} not found`)
		}

		try {
			const output = execSync(`git diff --name-only ${snapshot.commitHash}..HEAD`, { 
				cwd: this.cwd, 
				encoding: "utf8" 
			})
			return output.trim().split("\n").filter(file => file.length > 0)
		} catch (error) {
			return []
		}
	}

	/**
	 * Commit agent changes
	 */
	public async commitAgentChanges(agentId: string, message: string): Promise<string> {
		try {
			execSync("git add .", { cwd: this.cwd, stdio: "pipe" })
			execSync(`git commit -m "Agent ${agentId}: ${message}"`, { 
				cwd: this.cwd, 
				stdio: "pipe" 
			})

			const commitHash = execSync("git rev-parse HEAD", { 
				cwd: this.cwd, 
				encoding: "utf8" 
			}).trim()

			return commitHash
		} catch (error) {
			throw new Error(`Failed to commit changes: ${error instanceof Error ? error.message : String(error)}`)
		}
	}

	/**
	 * Check if repository has commits
	 */
	private async hasCommits(): Promise<boolean> {
		try {
			execSync("git rev-parse HEAD", { cwd: this.cwd, stdio: "pipe" })
			return true
		} catch {
			return false
		}
	}

	/**
	 * Get all snapshots for an agent
	 */
	public getAgentSnapshots(agentId: string): GitSnapshot[] {
		return Array.from(this.snapshots.values())
			.filter(snapshot => snapshot.agentId === agentId)
			.sort((a, b) => b.timestamp - a.timestamp)
	}

	/**
	 * Clean up old snapshots
	 */
	public cleanupOldSnapshots(maxAge: number = 24 * 60 * 60 * 1000): void {
		const cutoff = Date.now() - maxAge
		for (const [id, snapshot] of this.snapshots) {
			if (snapshot.timestamp < cutoff) {
				this.snapshots.delete(id)
			}
		}
	}

	/**
	 * Get repository info for UI display
	 */
	public async getRepositoryInfo(): Promise<{
		isInitialized: boolean
		branch: string
		lastCommit?: string
		uncommittedChanges: number
		snapshots: number
	}> {
		const status = await this.getGitStatus()
		
		let uncommittedChanges = 0
		if (status.isRepository) {
			try {
				const statusOutput = execSync("git status --porcelain", { 
					cwd: this.cwd, 
					encoding: "utf8" 
				})
				uncommittedChanges = statusOutput.trim().split("\n").filter(line => line.trim()).length
			} catch {
				// Ignore errors
			}
		}

		return {
			isInitialized: status.isRepository,
			branch: status.currentBranch,
			lastCommit: status.lastCommit?.hash.substring(0, 8),
			uncommittedChanges,
			snapshots: this.snapshots.size
		}
	}
}