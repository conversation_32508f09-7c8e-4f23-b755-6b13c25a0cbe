/**
 * @fileoverview Agent-to-Agent Communication Protocol
 * Based on Google's A2A (Agent2Agent) Protocol specification
 */

export interface A2AMessage {
	id: string
	role: "user" | "agent"
	parts: A2APart[]
	timestamp: number
}

export interface A2APart {
	type: "text" | "data" | "file"
	content: string | object
	metadata?: Record<string, any>
}

export interface A2ATask {
	id: string
	status: "pending" | "running" | "completed" | "failed" | "cancelled"
	context?: string
	messages: A2AMessage[]
	artifacts: A2AArtifact[]
	createdAt: number
	updatedAt: number
}

export interface A2AArtifact {
	id: string
	type: "document" | "code" | "analysis" | "knowledge"
	parts: A2APart[]
	metadata: Record<string, any>
}

export interface AgentCapability {
	name: string
	description: string
	inputTypes: string[]
	outputTypes: string[]
	parameters?: Record<string, any>
}

export interface AgentCard {
	id: string
	name: string
	description: string
	version: string
	capabilities: AgentCapability[]
	endpoint?: string
	authentication?: string[]
	metadata: Record<string, any>
}

export class AgentCommunicationProtocol {
	private agentCards: Map<string, AgentCard> = new Map()
	private activeTasks: Map<string, A2ATask> = new Map()
	private messageHandlers: Map<string, (message: A2AMessage) => Promise<A2AMessage>> = new Map()

	/**
	 * Register an agent with its capabilities
	 */
	public registerAgent(agentCard: AgentCard): void {
		this.agentCards.set(agentCard.id, agentCard)
		console.log(`Registered agent: ${agentCard.name} (${agentCard.id})`)
	}

	/**
	 * Discover available agents and their capabilities
	 */
	public discoverAgents(capability?: string): AgentCard[] {
		const agents = Array.from(this.agentCards.values())
		
		if (capability) {
			return agents.filter(agent => 
				agent.capabilities.some(cap => 
					cap.name.toLowerCase().includes(capability.toLowerCase())
				)
			)
		}
		
		return agents
	}

	/**
	 * Create a new task for agent collaboration
	 */
	public createTask(initiatorId: string, targetId: string, initialMessage: A2AMessage): A2ATask {
		const task: A2ATask = {
			id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			status: "pending",
			context: `${initiatorId}_to_${targetId}`,
			messages: [initialMessage],
			artifacts: [],
			createdAt: Date.now(),
			updatedAt: Date.now()
		}

		this.activeTasks.set(task.id, task)
		return task
	}

	/**
	 * Send a message between agents
	 */
	public async sendMessage(
		fromAgentId: string,
		toAgentId: string,
		taskId: string,
		message: A2AMessage
	): Promise<A2AMessage | null> {
		const task = this.activeTasks.get(taskId)
		if (!task) {
			throw new Error(`Task ${taskId} not found`)
		}

		const targetAgent = this.agentCards.get(toAgentId)
		if (!targetAgent) {
			throw new Error(`Target agent ${toAgentId} not found`)
		}

		// Add message to task
		task.messages.push(message)
		task.updatedAt = Date.now()
		task.status = "running"

		// Get message handler for target agent
		const handler = this.messageHandlers.get(toAgentId)
		if (!handler) {
			console.warn(`No message handler registered for agent ${toAgentId}`)
			return null
		}

		try {
			// Process message with target agent
			const response = await handler(message)
			
			// Add response to task
			task.messages.push(response)
			task.updatedAt = Date.now()
			
			return response
		} catch (error) {
			console.error(`Error processing message for agent ${toAgentId}:`, error)
			task.status = "failed"
			return null
		}
	}

	/**
	 * Register a message handler for an agent
	 */
	public registerMessageHandler(
		agentId: string,
		handler: (message: A2AMessage) => Promise<A2AMessage>
	): void {
		this.messageHandlers.set(agentId, handler)
	}

	/**
	 * Complete a task and generate artifacts
	 */
	public completeTask(taskId: string, artifacts: A2AArtifact[] = []): void {
		const task = this.activeTasks.get(taskId)
		if (!task) {
			throw new Error(`Task ${taskId} not found`)
		}

		task.status = "completed"
		task.artifacts = artifacts
		task.updatedAt = Date.now()
	}

	/**
	 * Get task status and progress
	 */
	public getTask(taskId: string): A2ATask | undefined {
		return this.activeTasks.get(taskId)
	}

	/**
	 * Get all active tasks
	 */
	public getActiveTasks(): A2ATask[] {
		return Array.from(this.activeTasks.values())
	}

	/**
	 * Cancel a task
	 */
	public cancelTask(taskId: string): void {
		const task = this.activeTasks.get(taskId)
		if (task) {
			task.status = "cancelled"
			task.updatedAt = Date.now()
		}
	}

	/**
	 * Request delegation from one agent to another
	 */
	public async requestDelegation(
		fromAgentId: string,
		toAgentId: string,
		capability: string,
		context: A2APart[],
		parameters?: Record<string, any>
	): Promise<A2ATask> {
		const delegationMessage: A2AMessage = {
			id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			role: "user",
			parts: [
				{
					type: "text",
					content: `Delegation request for capability: ${capability}`,
					metadata: { 
						type: "delegation_request",
						capability,
						parameters: parameters || {}
					}
				},
				...context
			],
			timestamp: Date.now()
		}

		const task = this.createTask(fromAgentId, toAgentId, delegationMessage)
		
		// Send the delegation message
		await this.sendMessage(fromAgentId, toAgentId, task.id, delegationMessage)
		
		return task
	}

	/**
	 * Create a knowledge sharing message for scholar agent
	 */
	public createKnowledgeRequest(
		fromAgentId: string,
		context: string,
		patterns: string[],
		insights: string[]
	): A2AMessage {
		return {
			id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			role: "user",
			parts: [
				{
					type: "text",
					content: "Knowledge extraction request",
					metadata: { type: "knowledge_request" }
				},
				{
					type: "data",
					content: {
						context,
						patterns,
						insights,
						extractionType: "learning_documentation"
					},
					metadata: { type: "learning_context" }
				}
			],
			timestamp: Date.now()
		}
	}

	/**
	 * Get communication statistics
	 */
	public getStats(): {
		registeredAgents: number
		activeTasks: number
		completedTasks: number
		failedTasks: number
		totalMessages: number
	} {
		const tasks = Array.from(this.activeTasks.values())
		
		return {
			registeredAgents: this.agentCards.size,
			activeTasks: tasks.filter(t => t.status === "running").length,
			completedTasks: tasks.filter(t => t.status === "completed").length,
			failedTasks: tasks.filter(t => t.status === "failed").length,
			totalMessages: tasks.reduce((sum, task) => sum + task.messages.length, 0)
		}
	}
}