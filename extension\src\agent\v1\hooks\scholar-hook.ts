/**
 * Scholar Hook - AI Learning System Entry Point
 * 
 * Coordinates the complete learning pipeline:
 * Context Detection → Compression → Knowledge Extraction → Storage → RAG
 */

import dedent from "dedent"
import { MainAgent } from "../main-agent"
import { BaseHook, HookOptions } from "./base-hook"
import { getCwd } from "../utils"
import * as fs from "fs/promises"
import * as path from "path"

// Import new learning modules
import { ContextProcessor } from '../scholar/context/context-processor'
import { LearningContext, CompressionResult } from '../scholar/types'

export interface ScholarHookOptions extends HookOptions {
	/**
	 * Trigger when learning opportunities are detected
	 */
	autoTrigger?: boolean
}

export class ScholarHook extends BaseHook {
	private options: ScholarHookOptions
	private contextProcessor: ContextProcessor

	constructor(options: ScholarHookOptions, MainAgent: MainAgent) {
		super(options, MainAgent)
		this.options = options
		this.contextProcessor = new ContextProcessor()
	}

	/**
	 * Check if we should execute the scholar hook
	 */
	private shouldExecute(): boolean {
		try {
			const history = this.MainAgent.getStateManager().state.apiConversationHistory
			
			// Don't execute if in sub-agent or if no recent activity
			const isInSubAgent = !!this.MainAgent.getStateManager().subAgentManager.currentSubAgentId
			const hasRecentActivity = history.length > 2
			
			// Look for learning opportunities in recent messages
			const recentMessages = history.slice(-3)
			const hasLearningOpportunity = this.detectLearningOpportunity(recentMessages)
			
			return hasRecentActivity && !isInSubAgent && hasLearningOpportunity
		} catch (e) {
			return false
		}
	}

	/**
	 * Detect if there's a learning opportunity in recent messages
	 */
	private detectLearningOpportunity(messages: any[]): boolean {
		const learningKeywords = [
			"successfully", "resolved", "fixed", "implemented", "optimized", 
			"discovered", "found", "pattern", "approach", "solution",
			"effective", "efficient", "best practice", "lesson learned"
		]
		
		const messageText = messages
			.map(msg => this.extractTextFromMessage(msg))
			.join(" ")
			.toLowerCase()
		
		return learningKeywords.some(keyword => messageText.includes(keyword))
	}

	/**
	 * Execute the scholar hook - New AI Learning Pipeline
	 */
	protected async executeHook(): Promise<string | null> {
		const ts = Date.now()
		
		try {
			if (!this.shouldExecute()) {
				return null
			}

			console.log("[ScholarHook] - Starting AI Learning Pipeline")
			
			// Step 1: Create learning context
			const learningContext = this.createLearningContext()
			
			this.MainAgent.taskExecutor.sayHook({
				hookName: "scholar",
				state: "pending",
				output: "Processing learning context...",
				input: "",
				ts,
				modelId: "scholar-pipeline",
			})

			// Step 2: Process context with AI compression
			const compressionResult = await this.contextProcessor.processLearningContext(learningContext)
			
			console.log(`[ScholarHook] - Context compressed with ratio: ${compressionResult.compressionRatio.toFixed(3)}`)
			console.log(`[ScholarHook] - Extracted ${compressionResult.coreInsights.length} insights, ${compressionResult.keyPatterns.length} patterns`)

			// Step 3: Save structured knowledge
			const success = await this.saveStructuredKnowledge(compressionResult, learningContext)
			
			if (success) {
				console.log("[ScholarHook] - Knowledge successfully processed and stored")
				
				this.MainAgent.taskExecutor.sayHook({
					hookName: "scholar",
					state: "completed",
					output: `Processed ${compressionResult.coreInsights.length} insights and ${compressionResult.keyPatterns.length} patterns`,
					input: "",
					ts,
					modelId: "scholar-pipeline",
				})
				
				// Create summary for main agent
				return dedent`## 🧠 Scholar Learning Pipeline Results ##
				
				**Context Compression**: ${(compressionResult.compressionRatio * 100).toFixed(1)}% retention
				**Insights Extracted**: ${compressionResult.coreInsights.length}
				**Patterns Identified**: ${compressionResult.keyPatterns.length}
				**Knowledge Items**: ${compressionResult.actionableKnowledge.length}
				**Confidence**: ${(compressionResult.confidence * 100).toFixed(1)}%
				
				### Key Insights:
				${compressionResult.coreInsights.map(insight => `• ${insight}`).join('\n')}
				
				### Patterns Detected:
				${compressionResult.keyPatterns.map(pattern => `• ${pattern.type}: ${pattern.description}`).join('\n')}
				
				*Knowledge has been structured and stored in the .Ai knowledge base for future retrieval.*
				
				## End of Scholar Processing ##`
			} else {
				this.MainAgent.taskExecutor.sayHook({
					hookName: "scholar",
					state: "error",
					output: "Failed to save knowledge",
					input: "",
					ts,
					modelId: "scholar-pipeline"
				})
				return null
			}
			
		} catch (error) {
			console.error("[ScholarHook] - Learning pipeline failed:", error)
			
			this.MainAgent.taskExecutor.sayHook({
				hookName: "scholar",
				state: "error",
				output: `Pipeline error: ${error instanceof Error ? error.message : String(error)}`,
				input: "",
				ts,
				modelId: "scholar-pipeline"
			})
			
			return null
		}
	}

	/**
	 * Create learning context from current state
	 */
	private createLearningContext(): LearningContext {
		const history = this.MainAgent.getStateManager().state.apiConversationHistory
		const recentMessages = history.slice(-5) // Last 5 messages for context
		
		return {
			id: `learning-${Date.now()}`,
			originalContext: recentMessages.map(msg => this.extractTextFromMessage(msg)).join('\n\n'),
			taskContext: this.getTaskContext(),
			messages: recentMessages,
			timestamp: Date.now(),
			metadata: {
				messageCount: recentMessages.length,
				totalLength: recentMessages.reduce((sum, msg) => sum + this.extractTextFromMessage(msg).length, 0)
			}
		}
	}

	/**
	 * Get task context from first message
	 */
	private getTaskContext(): string {
		const history = this.MainAgent.getStateManager().state.apiConversationHistory
		if (history.length === 0) {
			return ""
		}
		
		const firstMessage = history[0]
		const messageText = this.extractTextFromMessage(firstMessage)
		
		// Extract task from <task></task> tags if present
		const taskMatch = messageText.match(/<task>(.*?)<\/task>/s)
		return taskMatch ? taskMatch[1].trim() : messageText.substring(0, 200)
	}

	/**
	 * Save structured knowledge using new architecture
	 */
	private async saveStructuredKnowledge(
		compressionResult: CompressionResult, 
		context: LearningContext
	): Promise<boolean> {
		try {
			const projectRoot = this.findProjectRoot()
			const aiDir = path.join(projectRoot, ".Ai")
			
			// Ensure .Ai directory structure
			await this.ensureAiDirectoryStructure(aiDir)
			
			// Save insights
			for (const insight of compressionResult.coreInsights) {
				await this.saveInsight(aiDir, insight, context)
			}
			
			// Save patterns
			for (const pattern of compressionResult.keyPatterns) {
				await this.savePattern(aiDir, pattern, context)
			}
			
			// Save actionable knowledge
			for (const knowledge of compressionResult.actionableKnowledge) {
				await this.saveKnowledge(aiDir, knowledge, context)
			}
			
			// Update index
			await this.updateKnowledgeIndex(aiDir, compressionResult, context)
			
			return true
		} catch (error) {
			console.error("[ScholarHook] - Failed to save structured knowledge:", error)
			return false
		}
	}

	/**
	 * Find project root directory
	 */
	private findProjectRoot(): string {
		let currentDir = getCwd()
		const maxDepth = 10
		let depth = 0
		
		while (depth < maxDepth) {
			try {
				const indicators = ['package.json', '.git', 'tsconfig.json', 'pyproject.toml', 'Cargo.toml', 'go.mod']
				for (const indicator of indicators) {
					const indicatorPath = path.join(currentDir, indicator)
					try {
						require('fs').accessSync(indicatorPath)
						return currentDir
					} catch {
						// Continue checking
					}
				}
				
				const parentDir = path.dirname(currentDir)
				if (parentDir === currentDir) {
					break // Reached root
				}
				currentDir = parentDir
				depth++
			} catch {
				break
			}
		}
		
		return getCwd()
	}

	/**
	 * Ensure .Ai directory structure
	 */
	private async ensureAiDirectoryStructure(aiDir: string): Promise<void> {
		const directories = [
			path.join(aiDir, "Knowledge", "Insights"),
			path.join(aiDir, "Knowledge", "Patterns"),
			path.join(aiDir, "Knowledge", "Solutions"),
			path.join(aiDir, "Knowledge", "Workflows"),
			path.join(aiDir, "Vectors"),
			path.join(aiDir, "Memory", "short-term"),
			path.join(aiDir, "Memory", "long-term"),
			path.join(aiDir, "Config")
		]
		
		for (const dir of directories) {
			await fs.mkdir(dir, { recursive: true })
		}
	}

	/**
	 * Save individual insight
	 */
	private async saveInsight(aiDir: string, insight: string, context: LearningContext): Promise<void> {
		const timestamp = new Date().toISOString().slice(0, 16).replace(/[:-]/g, '')
		const filename = `insight_${timestamp}.md`
		const filePath = path.join(aiDir, "Knowledge", "Insights", filename)
		
		const content = dedent`
			# Insight: ${insight.substring(0, 50)}...
			
			**Generated**: ${new Date().toISOString()}
			**Source**: Scholar Learning Pipeline
			**Context**: ${context.taskContext}
			
			## Content
			
			${insight}
			
			## Metadata
			
			- **Type**: Core Insight
			- **Confidence**: High
			- **Applicability**: General Development
			
			---
			*Auto-generated by Scholar Agent*
		`
		
		await fs.writeFile(filePath, content, "utf8")
	}

	/**
	 * Save pattern
	 */
	private async savePattern(aiDir: string, pattern: any, context: LearningContext): Promise<void> {
		const timestamp = new Date().toISOString().slice(0, 16).replace(/[:-]/g, '')
		const filename = `pattern_${pattern.type}_${timestamp}.md`
		const filePath = path.join(aiDir, "Knowledge", "Patterns", filename)
		
		const content = dedent`
			# Pattern: ${pattern.description}
			
			**Type**: ${pattern.type}
			**Frequency**: ${pattern.frequency}
			**Effectiveness**: ${pattern.effectiveness}
			**Generated**: ${new Date().toISOString()}
			
			## Description
			
			${pattern.description}
			
			## Context
			
			${pattern.context}
			
			## Metadata
			
			- **Pattern ID**: ${pattern.id}
			- **Detection Method**: Automatic
			- **Confidence**: ${pattern.effectiveness}
			
			---
			*Auto-generated by Scholar Agent*
		`
		
		await fs.writeFile(filePath, content, "utf8")
	}

	/**
	 * Save knowledge item
	 */
	private async saveKnowledge(aiDir: string, knowledge: any, context: LearningContext): Promise<void> {
		const timestamp = new Date().toISOString().slice(0, 16).replace(/[:-]/g, '')
		const filename = `knowledge_${knowledge.category}_${timestamp}.md`
		const categoryDir = path.join(aiDir, "Knowledge", knowledge.category.charAt(0).toUpperCase() + knowledge.category.slice(1))
		
		await fs.mkdir(categoryDir, { recursive: true })
		const filePath = path.join(categoryDir, filename)
		
		const content = dedent`
			# ${knowledge.title}
			
			**Category**: ${knowledge.category}
			**Importance**: ${knowledge.importance}
			**Tags**: ${knowledge.tags.join(', ')}
			**Generated**: ${new Date().toISOString()}
			
			## Content
			
			${knowledge.content}
			
			## Applicability
			
			${knowledge.applicability.join(', ')}
			
			## Metadata
			
			- **Knowledge ID**: ${knowledge.id}
			- **Use Count**: ${knowledge.useCount}
			- **Created**: ${knowledge.created}
			
			---
			*Auto-generated by Scholar Agent*
		`
		
		await fs.writeFile(filePath, content, "utf8")
	}

	/**
	 * Update knowledge index
	 */
	private async updateKnowledgeIndex(
		aiDir: string, 
		compressionResult: CompressionResult, 
		context: LearningContext
	): Promise<void> {
		const indexPath = path.join(aiDir, "Knowledge", "INDEX.md")
		const timestamp = new Date().toISOString()
		
		const indexEntry = dedent`
			## Learning Session - ${timestamp}
			
			**Context**: ${context.taskContext}
			**Compression Ratio**: ${(compressionResult.compressionRatio * 100).toFixed(1)}%
			**Confidence**: ${(compressionResult.confidence * 100).toFixed(1)}%
			
			### Extracted:
			- **Insights**: ${compressionResult.coreInsights.length}
			- **Patterns**: ${compressionResult.keyPatterns.length}
			- **Knowledge Items**: ${compressionResult.actionableKnowledge.length}
			
			---
			
		`
		
		// Read existing index or create new one
		let indexContent = ""
		try {
			indexContent = await fs.readFile(indexPath, "utf8")
		} catch {
			indexContent = dedent`
				# Scholar Agent Knowledge Index
				
				This index tracks all learning sessions and extracted knowledge.
				
				---
				
			`
		}
		
		// Prepend new entry
		const updatedContent = indexContent.replace(
			"---\n\n", 
			`---\n\n${indexEntry}`
		)
		
		await fs.writeFile(indexPath, updatedContent, "utf8")
	}

	/**
	 * Extract text content from message
	 */
	private extractTextFromMessage(message: any): string {
		if (!message?.content) {
			return ""
		}
		
		if (typeof message.content === "string") {
			return message.content
		}
		
		if (Array.isArray(message.content)) {
			return message.content
				.filter((block: any) => block.type === "text")
				.map((block: any) => block.text)
				.join(" ")
		}
		
		return ""
	}
}