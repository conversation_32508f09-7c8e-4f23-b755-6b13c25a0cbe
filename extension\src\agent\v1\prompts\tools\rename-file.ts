import { ToolPromptSchema } from "../utils/utils"

export const renameFilePrompt: ToolPromptSchema = {
	name: "rename_file",
	description:
		"Rename a file or directory. This tool changes the name of an existing file or directory while keeping it in the same location.",
	parameters: {
		path: {
			type: "string",
			description: "Path to the file or directory to rename. Can be relative or absolute path.",
			required: true,
		},
		new_name: {
			type: "string", 
			description: "New name for the file or directory. Should include file extension if applicable.",
			required: true,
		},
	},
	capabilities: [
		"Rename files and directories",
		"Preserve file location while changing name",
		"Support for all file types and extensions",
		"Handle both relative and absolute paths",
	],
	examples: [
		{
			description: "Rename a TypeScript file",
			output: `<rename_file>
<path>src/components/Button.tsx</path>
<new_name>CustomButton.tsx</new_name>
</rename_file>`,
		},
		{
			description: "Rename a directory",
			output: `<rename_file>
<path>src/utils</path>
<new_name>helpers</new_name>
</rename_file>`,
		},
		{
			description: "Rename a configuration file",
			output: `<rename_file>
<path>config.json</path>
<new_name>app-config.json</new_name>
</rename_file>`,
		},
	],
}