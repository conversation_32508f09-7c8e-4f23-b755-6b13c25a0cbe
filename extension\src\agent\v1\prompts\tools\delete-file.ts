import { ToolPromptSchema } from "../utils/utils"

export const deleteFilePrompt: ToolPromptSchema = {
	name: "delete_file",
	description:
		"Delete a file or directory. This tool permanently removes files or directories from the filesystem. Use with caution as this operation cannot be undone.",
	parameters: {
		path: {
			type: "string",
			description: "Path to the file or directory to delete. Can be relative or absolute path.",
			required: true,
		},
		force: {
			type: "boolean",
			description: "Whether to force deletion of non-empty directories (default: false). When true, recursively deletes directory contents.",
			required: false,
		},
	},
	capabilities: [
		"Delete files and directories",
		"Force deletion of non-empty directories",
		"Handle both relative and absolute paths",
		"Provide safety checks before deletion",
	],
	examples: [
		{
			description: "Delete a single file",
			output: `<delete_file>
<path>temp/old-file.txt</path>
</delete_file>`,
		},
		{
			description: "Delete an empty directory",
			output: `<delete_file>
<path>build/temp</path>
</delete_file>`,
		},
		{
			description: "Force delete a directory with contents",
			output: `<delete_file>
<path>node_modules/old-package</path>
<force>true</force>
</delete_file>`,
		},
	],
}