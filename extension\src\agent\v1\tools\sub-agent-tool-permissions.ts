/**
 * @fileoverview Sub-Agent Tool Permissions - Manages tool access permissions for different sub-agent types
 */

import { SpawnAgentOptions } from "./schema/agents/agent-spawner"
import { ToolName } from "./types"

export interface ToolPermissions {
    allowed: ToolName[]
    restricted: ToolName[]
    description: string
}

/**
 * Tool permissions for different sub-agent types
 */
export const SUB_AGENT_TOOL_PERMISSIONS: Record<SpawnAgentOptions, ToolPermissions> = {
    planner: {
        allowed: [
            // Read-only tools for analysis
            "read_file",
            "list_files",
            "search_files",
            "search_symbol",
            "explore_repo_folder",
            "url_screenshot",
            // Planning-specific tools
            "ask_followup_question",
            "exit_agent"
        ],
        restricted: [
            // No file modification tools
            "file_editor",
            "move_file",
            "rename_file",
            "delete_file",
            // No execution tools
            "execute_command",
            "server_runner",
            // No spawning or completion
            "spawn_agent",
            "attempt_completion"
        ],
        description: "Planning agents focus on analysis and planning, with read-only access to codebase"
    },

    sub_task: {
        allowed: [
            // Full file access
            "read_file",
            "file_editor",
            "move_file",
            "rename_file",
            "delete_file",
            // Directory operations
            "list_files",
            "explore_repo_folder",
            // Search capabilities
            "search_files",
            "search_symbol",
            // Execution capabilities
            "execute_command",
            // Communication
            "ask_followup_question",
            "url_screenshot",
            // Exit capability
            "exit_agent"
        ],
        restricted: [
            // Cannot spawn other agents
            "spawn_agent",
            // Cannot complete main task
            "attempt_completion",
            // Cannot manage servers (main agent responsibility)
            "server_runner"
        ],
        description: "Sub-task agents have comprehensive tool access for implementation work"
    },

    coder: {
        allowed: [
            // Full file access
            "read_file",
            "file_editor",
            "move_file",
            "rename_file",
            "delete_file",
            // Directory operations
            "list_files",
            "explore_repo_folder",
            // Search capabilities
            "search_files",
            "search_symbol",
            // Execution capabilities
            "execute_command",
            "server_runner",
            // Communication
            "ask_followup_question",
            "url_screenshot",
            // Exit capability
            "exit_agent"
        ],
        restricted: [
            // Cannot spawn other agents
            "spawn_agent",
            // Cannot complete main task (should use exit_agent instead)
            "attempt_completion"
        ],
        description: "Coder agents have nearly full tool access for comprehensive development work"
    },

    analyzer: {
        allowed: [
            // Read-only file access for analysis
            "read_file",
            "list_files",
            "search_files",
            "search_symbol",
            "explore_repo_folder",
            // Command execution for analysis (dependency checks, builds, etc.)
            "execute_command",
            // Communication and documentation
            "ask_followup_question",
            "url_screenshot",
            "add_interested_file",
            // Exit capability
            "exit_agent"
        ],
        restricted: [
            // No file modification tools
            "file_editor",
            "move_file",
            "rename_file",
            "delete_file",
            // No server management
            "server_runner",
            // No spawning or completion
            "spawn_agent",
            "attempt_completion"
        ],
        description: "Analyzer agents focus on project analysis with read access and command execution for analysis purposes"
    },

    researcher: {
        allowed: [
            // Read-only file access for research
            "read_file",
            "list_files",
            "search_files",
            "search_symbol",
            "explore_repo_folder",
            // Command execution for research (running tools, gathering info)
            "execute_command",
            // Communication and external research
            "ask_followup_question",
            "url_screenshot",
            "add_interested_file",
            // Exit capability
            "exit_agent"
        ],
        restricted: [
            // No file modification tools
            "file_editor",
            "move_file",
            "rename_file",
            "delete_file",
            // No server management
            "server_runner",
            // No spawning or completion
            "spawn_agent",
            "attempt_completion"
        ],
        description: "Researcher agents focus on codebase analysis and high-value recommendations without learning tool access"
    }
}

/**
 * Check if a sub-agent type is allowed to use a specific tool
 */
export function isToolAllowedForAgent(agentType: SpawnAgentOptions, toolName: ToolName): boolean {
    const permissions = SUB_AGENT_TOOL_PERMISSIONS[agentType]
    return permissions.allowed.includes(toolName)
}

/**
 * Get all allowed tools for a sub-agent type
 */
export function getAllowedToolsForAgent(agentType: SpawnAgentOptions): ToolName[] {
    return SUB_AGENT_TOOL_PERMISSIONS[agentType].allowed
}

/**
 * Get all restricted tools for a sub-agent type
 */
export function getRestrictedToolsForAgent(agentType: SpawnAgentOptions): ToolName[] {
    return SUB_AGENT_TOOL_PERMISSIONS[agentType].restricted
}

/**
 * Get tool permissions description for a sub-agent type
 */
export function getToolPermissionsDescription(agentType: SpawnAgentOptions): string {
    return SUB_AGENT_TOOL_PERMISSIONS[agentType].description
}

/**
 * Validate tool execution permission for a sub-agent
 */
export function validateToolPermission(agentType: SpawnAgentOptions, toolName: ToolName): {
    allowed: boolean
    reason?: string
} {
    const permissions = SUB_AGENT_TOOL_PERMISSIONS[agentType]

    if (permissions.allowed.includes(toolName)) {
        return { allowed: true }
    }

    if (permissions.restricted.includes(toolName)) {
        return {
            allowed: false,
            reason: `Tool '${toolName}' is restricted for ${agentType} agents. ${permissions.description}`
        }
    }

    // Tool not explicitly listed - default to restricted for safety
    return {
        allowed: false,
        reason: `Tool '${toolName}' is not explicitly allowed for ${agentType} agents`
    }
}