/**
 * Semantic Analyzer - 语义分析器
 * 
 * 分析上下文的语义结构，识别：
 * 1. 意图和目标
 * 2. 情感倾向
 * 3. 重要性权重
 * 4. 语义关系
 */

import { LearningContext } from '../types'

export interface SemanticAnalysis {
	intent: string
	sentiment: 'positive' | 'negative' | 'neutral'
	importance: number
	topics: string[]
	entities: string[]
	relationships: Array<{source: string, target: string, type: string}>
}

export class SemanticAnalyzer {
	private readonly IMPORTANCE_KEYWORDS = {
		high: ['critical', 'important', 'essential', 'must', 'required', 'urgent'],
		medium: ['should', 'recommend', 'suggest', 'prefer', 'better'],
		low: ['could', 'might', 'optional', 'consider', 'maybe']
	}

	private readonly INTENT_PATTERNS = {
		'problem-solving': ['fix', 'solve', 'resolve', 'debug', 'troubleshoot'],
		'implementation': ['create', 'build', 'implement', 'develop', 'code'],
		'optimization': ['optimize', 'improve', 'enhance', 'refactor', 'performance'],
		'learning': ['understand', 'learn', 'explore', 'investigate', 'research'],
		'configuration': ['configure', 'setup', 'install', 'deploy', 'settings']
	}

	/**
	 * 分析学习上下文的语义
	 */
	async analyzeContext(context: LearningContext): Promise<SemanticAnalysis> {
		console.log(`[SemanticAnalyzer] Analyzing context: ${context.id}`)

		const fullText = this.extractFullText(context)
		
		return {
			intent: this.detectIntent(fullText),
			sentiment: this.analyzeSentiment(fullText),
			importance: this.calculateImportance(fullText),
			topics: this.extractTopics(fullText),
			entities: this.extractEntities(fullText),
			relationships: this.extractRelationships(fullText)
		}
	}

	/**
	 * 提取完整文本
	 */
	private extractFullText(context: LearningContext): string {
		const texts = [context.originalContext, context.taskContext]
		
		for (const message of context.messages) {
			texts.push(this.extractTextFromMessage(message))
		}

		return texts.filter(Boolean).join(' ')
	}

	/**
	 * 检测意图
	 */
	private detectIntent(text: string): string {
		const textLower = text.toLowerCase()
		let maxScore = 0
		let detectedIntent = 'general'

		for (const [intent, keywords] of Object.entries(this.INTENT_PATTERNS)) {
			const score = keywords.reduce((sum, keyword) => {
				const matches = (textLower.match(new RegExp(keyword, 'g')) || []).length
				return sum + matches
			}, 0)

			if (score > maxScore) {
				maxScore = score
				detectedIntent = intent
			}
		}

		return detectedIntent
	}

	/**
	 * 分析情感倾向
	 */
	private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
		const positiveWords = [
			'success', 'successful', 'good', 'great', 'excellent', 'perfect', 
			'works', 'working', 'solved', 'fixed', 'completed', 'done'
		]
		
		const negativeWords = [
			'error', 'fail', 'failed', 'problem', 'issue', 'bug', 'wrong', 
			'broken', 'not working', 'difficult', 'hard', 'stuck'
		]

		const textLower = text.toLowerCase()
		
		const positiveScore = positiveWords.reduce((sum, word) => 
			sum + (textLower.match(new RegExp(word, 'g')) || []).length, 0
		)
		
		const negativeScore = negativeWords.reduce((sum, word) => 
			sum + (textLower.match(new RegExp(word, 'g')) || []).length, 0
		)

		if (positiveScore > negativeScore * 1.2) return 'positive'
		if (negativeScore > positiveScore * 1.2) return 'negative'
		return 'neutral'
	}

	/**
	 * 计算重要性分数
	 */
	private calculateImportance(text: string): number {
		const textLower = text.toLowerCase()
		let score = 0.5 // Base score

		// Check for importance keywords
		for (const [level, keywords] of Object.entries(this.IMPORTANCE_KEYWORDS)) {
			const matches = keywords.reduce((sum, keyword) => 
				sum + (textLower.match(new RegExp(keyword, 'g')) || []).length, 0
			)

			switch (level) {
				case 'high':
					score += matches * 0.2
					break
				case 'medium':
					score += matches * 0.1
					break
				case 'low':
					score -= matches * 0.05
					break
			}
		}

		// Length factor (longer contexts might be more important)
		const lengthFactor = Math.min(text.length / 1000, 1) * 0.1
		score += lengthFactor

		// Code presence factor
		const codeBlocks = (text.match(/```[\s\S]*?```/g) || []).length
		score += codeBlocks * 0.05

		return Math.max(0, Math.min(1, score))
	}

	/**
	 * 提取主题
	 */
	private extractTopics(text: string): string[] {
		const topics: string[] = []
		
		// Technical topics
		const techPatterns = [
			/\b(react|vue|angular|javascript|typescript|python|java|c\+\+|rust|go)\b/gi,
			/\b(database|sql|mongodb|redis|postgresql)\b/gi,
			/\b(api|rest|graphql|microservice|docker|kubernetes)\b/gi,
			/\b(testing|unit test|integration|deployment|ci\/cd)\b/gi
		]

		for (const pattern of techPatterns) {
			const matches = text.match(pattern) || []
			topics.push(...matches.map(match => match.toLowerCase()))
		}

		// Remove duplicates and limit
		return [...new Set(topics)].slice(0, 5)
	}

	/**
	 * 提取实体
	 */
	private extractEntities(text: string): string[] {
		const entities: string[] = []

		// File names
		const fileMatches = text.match(/\b[\w-]+\.(js|ts|py|java|cpp|rs|go|md|json|yaml|yml)\b/g) || []
		entities.push(...fileMatches)

		// Function/method names
		const functionMatches = text.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\s*\(/g) || []
		entities.push(...functionMatches.map(match => match.replace(/\s*\($/, '')))

		// Class names (PascalCase)
		const classMatches = text.match(/\b[A-Z][a-zA-Z0-9]*(?:[A-Z][a-zA-Z0-9]*)*\b/g) || []
		entities.push(...classMatches)

		// Remove duplicates and limit
		return [...new Set(entities)].slice(0, 10)
	}

	/**
	 * 提取关系
	 */
	private extractRelationships(text: string): Array<{source: string, target: string, type: string}> {
		const relationships: Array<{source: string, target: string, type: string}> = []

		// Simple relationship patterns
		const relationPatterns = [
			{ pattern: /(\w+)\s+uses?\s+(\w+)/gi, type: 'uses' },
			{ pattern: /(\w+)\s+implements?\s+(\w+)/gi, type: 'implements' },
			{ pattern: /(\w+)\s+extends?\s+(\w+)/gi, type: 'extends' },
			{ pattern: /(\w+)\s+depends?\s+on\s+(\w+)/gi, type: 'depends-on' }
		]

		for (const { pattern, type } of relationPatterns) {
			let match
			while ((match = pattern.exec(text)) !== null) {
				relationships.push({
					source: match[1].toLowerCase(),
					target: match[2].toLowerCase(),
					type
				})
			}
		}

		return relationships.slice(0, 5)
	}

	/**
	 * 从消息中提取文本
	 */
	private extractTextFromMessage(message: any): string {
		if (!message?.content) {
			return ""
		}
		
		if (typeof message.content === "string") {
			return message.content
		}
		
		if (Array.isArray(message.content)) {
			return message.content
				.filter((block: any) => block.type === "text")
				.map((block: any) => block.text)
				.join(" ")
		}
		
		return ""
	}
}