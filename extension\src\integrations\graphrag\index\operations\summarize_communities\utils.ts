/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Utilities for community summarization.
 */

import { DataFrame } from '../../../data-model/types';

/**
 * Get levels from nodes DataFrame.
 */
export function getLevels(nodes: DataFrame): number[] {
    const levels = new Set<number>();
    
    nodes.data.forEach(row => {
        if (row.level !== undefined && row.level !== null) {
            levels.add(parseInt(String(row.level), 10));
        }
    });
    
    return Array.from(levels).sort((a, b) => a - b);
}