import { vi } from 'vitest'

// Mock serialize-error
vi.mock('serialize-error', () => ({
  serializeError: (error: any) => ({
    name: error?.name || 'Error',
    message: error?.message || 'Unknown error',
    stack: error?.stack || 'No stack trace'
  })
}))

// Mock path helpers
vi.mock('../../utils', () => ({
  getReadablePath: (filePath: string, cwd: string) => {
    return filePath.startsWith(cwd) ? filePath.substring(cwd.length + 1) : filePath
  }
}))

// Global test setup
beforeEach(() => {
  vi.clearAllMocks()
})