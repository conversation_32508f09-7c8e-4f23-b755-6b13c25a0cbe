/**
 * Scholar Agent - AI Learning System
 * 
 * A comprehensive learning system that mimics human memory patterns:
 * Context Compression → Structured Storage → Intelligent Retrieval
 */

// Core modules (implemented)
export { ContextProcessor } from './context/context-processor'

// Future modules (to be implemented)
// export { KnowledgeExtractor } from './knowledge/knowledge-extractor'
// export { KnowledgeGraphBuilder } from './graph/graph-builder'
// export { VectorStoreManager } from './vector/vector-store'
// export { RAGEngine } from './rag/rag-engine'
// export { StorageManager } from './storage/storage-manager'

// Types
export * from './types'