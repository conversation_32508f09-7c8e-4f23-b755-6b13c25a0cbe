// schema/delete_file.ts
import { z } from "zod"

/**
 * @tool delete_file
 * @description SINGLE FILE DELETION TOOL - For deleting ONE file or directory
 * 
 * This tool is specifically designed for single file/directory deletion.
 * Use this when you need to delete exactly ONE file or directory.
 * 
 * SINGLE FILE MODE: For deleting ONE file/directory
 *    Parameters: path, force (optional), dry_run (optional)
 *    Use case: Delete one specific item
 *    Benefits: Simple, focused, clear intent
 * 
 * PREVIEW FUNCTIONALITY (dry_run):
 * - Set dry_run=true to preview deletion without actually deleting
 * - Shows exactly what file/directory would be deleted
 * - Safe way to verify the operation before execution
 * 
 * FORCE DELETION:
 * - force=false: Only delete empty directories and files (default)
 * - force=true: Delete non-empty directories recursively
 * - Use with extreme caution - cannot be undone
 * 
 * SAFETY FEATURES:
 * - Dry run mode for safe preview
 * - Path validation and security checks
 * - Clear error messages for permission issues
 * 
 * WHEN TO USE:
 * - Use this tool when you need to delete exactly ONE file or directory
 * - For removing a specific file that's no longer needed
 * - For deleting a single directory (empty or with force=true)
 * 
 * DO NOT USE for multiple files - use batch_delete_files instead
 * @example (SINGLE FILE MODE - Delete one file)
 * ```xml
 * <tool name="delete_file">
 *   <path>temp/old_file.txt</path>
 * </tool>
 * ```
 * @example (SINGLE FILE MODE - Delete one directory with force)
 * ```xml
 * <tool name="delete_file">
 *   <path>build/cache</path>
 *   <force>true</force>
 * </tool>
 * ```
 * @example (BATCH MODE - Delete multiple temporary files)
 * ```xml
 * <tool name="delete_file">
 *   <files>
 *     <file>
 *       <path>temp/cache.txt</path>
 *     </file>
 *     <file>
 *       <path>temp/logs.txt</path>
 *     </file>
 *     <file>
 *       <path>temp/session.json</path>
 *     </file>
 *   </files>
 * </tool>
 * ```
 * @example (BATCH MODE - Clean up build artifacts)
 * ```xml
 * <tool name="delete_file">
 *   <files>
 *     <file>
 *       <path>dist</path>
 *       <force>true</force>
 *     </file>
 *     <file>
 *       <path>build</path>
 *       <force>true</force>
 *     </file>
 *     <file>
 *       <path>node_modules/.cache</path>
 *       <force>true</force>
 *     </file>
 *   </files>
 * </tool>
 * ```
 */
const schema = z.object({
  path: z.string().describe("The path of the file or directory to delete (relative to the current working directory)."),
  force: z.boolean().optional().describe("Force deletion even if the file/directory is not empty or protected. Defaults to false."),
  dry_run: z.boolean().optional().describe("Preview deletion without applying it. Defaults to false."),
})

const examples = [
  // Single file examples
  `<delete_file>
  <path>temp/old_file.txt</path>
</delete_file>`,

  `<delete_file>
  <path>build/cache</path>
  <force>true</force>
  <dry_run>true</dry_run>
</delete_file>`,

  `<delete_file>
  <path>src/deprecated.js</path>
  <dry_run>true</dry_run>
</delete_file>`,

  `<delete_file>
  <path>node_modules/.cache</path>
  <force>true</force>
</delete_file>`,
]

export const deleteFileTool = {
  schema: {
    name: "delete_file",
    schema,
  },
  examples,
}

export type DeleteFileToolParams = {
  name: "delete_file"
  input: z.infer<typeof schema>
}