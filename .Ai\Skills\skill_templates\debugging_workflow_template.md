# 调试工作流程技能模板

## 技能基本信息
- **技能名称**: systematic_debugging_workflow
- **技能类别**: 问题解决
- **适用场景**: 代码错误、语法问题、逻辑错误
- **成功率目标**: 90%+

## 详细工作流程

### 第1步: 问题识别
- **工具**: read_file
- **目标**: 理解错误现象和上下文
- **模板**: 
  ```
  1. 读取报错文件
  2. 识别错误类型和位置
  3. 分析错误影响范围
  ```
- **预期结果**: 清晰的问题定义

### 第2步: 模式搜索
- **工具**: search_files
- **目标**: 查找类似问题和解决方案
- **模板**:
  ```
  1. 搜索相关错误模式
  2. 查找类似代码结构
  3. 识别潜在解决方案
  ```
- **预期结果**: 相关案例和解决思路

### 第3步: 针对性修复
- **工具**: file_editor
- **目标**: 应用精确的修复方案
- **模板**:
  ```
  1. 基于分析结果制定修复计划
  2. 应用最小化修改
  3. 保持代码一致性
  ```
- **预期结果**: 错误得到修复

### 第4步: 验证测试
- **工具**: execute_command
- **目标**: 确认修复效果
- **模板**:
  ```
  1. 运行相关测试
  2. 验证功能正常
  3. 检查无副作用
  ```
- **预期结果**: 问题完全解决

## 常见陷阱和解决方案

### 陷阱1: 过度修改
- **问题**: 修改范围过大，引入新问题
- **解决方案**: 采用最小化修改原则
- **预防措施**: 每次只修改一个问题点

### 陷阱2: 忽略依赖关系
- **问题**: 修复一个问题导致其他问题
- **解决方案**: 分析代码依赖关系
- **预防措施**: 使用search_files查找相关引用

### 陷阱3: 缺乏验证
- **问题**: 修复后没有充分测试
- **解决方案**: 建立完整的验证流程
- **预防措施**: 每次修复后都要运行测试

## 成功指标

### 主要指标
- 问题完全解决
- 代码功能正常
- 无新增错误

### 次要指标
- 修复时间合理
- 代码质量保持
- 用户满意度高

## 触发关键词
- "代码错误"
- "调试"
- "语法错误"
- "程序异常"
- "bug修复"

## 应用约束
- 必须先理解问题再修复
- 保持代码风格一致
- 避免破坏性修改
- 确保向后兼容

## 相关技能
- error_recovery
- code_analysis
- testing_workflow
- refactoring_process

## 使用示例

### 示例1: React组件错误
```
问题: React组件渲染失败
工具序列: read_file -> search_files -> file_editor -> execute_command
结果: 成功修复组件渲染问题
```

### 示例2: TypeScript类型错误
```
问题: TypeScript编译错误
工具序列: read_file -> search_files -> file_editor -> execute_command
结果: 类型错误得到解决
```

## 技能进化记录

### 版本1.0
- 基础调试流程
- 成功率: 78%

### 版本2.0
- 增加模式搜索步骤
- 成功率: 85%

### 版本3.0
- 优化验证流程
- 成功率: 92%

## 学习调用示例

```xml
<learning>
  <action>incremental_update</action>
  <skillName>systematic_debugging_workflow</skillName>
  <newExperience>{
    "newExample": "成功调试React hooks依赖数组问题",
    "additionalRule": "调试React组件时总是检查useEffect依赖",
    "improvedWorkflowStep": "在应用修复前添加依赖分析步骤"
  }</newExperience>
  <successOutcome>true</successOutcome>
</learning>
```