// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Noun phrase extractor based on dependency parsing and NER using SpaCy.
 */

import { BaseNounPhraseExtractor } from './base.js';
import { hasValidTokenLength, isCompound, isValidEntity } from './np-validator.js';

/**
 * SpaCy token interface
 */
interface SpacyToken {
  text: string;
  pos_: string;
  is_space: boolean;
  is_punct: boolean;
}

/**
 * SpaCy span interface
 */
interface SpacySpan {
  text: string;
  label_?: string;
  tokens?: SpacyToken[];
}

/**
 * SpaCy document interface
 */
interface SpacyDoc {
  ents: SpacySpan[];
  noun_chunks: SpacySpan[];
}

/**
 * SpaCy NLP interface
 */
interface SpacyNLP {
  (text: string): SpacyDoc;
}

/**
 * Noun phrase extractor based on dependency parsing and NER using SpaCy.
 */
export class SyntacticNounPhraseExtractor extends BaseNounPhraseExtractor {
  private includeNamedEntities: boolean;
  private excludeEntityTags: string[];
  private excludePosTags: string[];
  private nlp: SpacyNLP;

  constructor(
    modelName: string,
    maxWordLength: number,
    includeNamedEntities: boolean,
    excludeEntityTags: string[],
    excludePosTags: string[],
    excludeNouns: string[],
    wordDelimiter: string
  ) {
    super(modelName, maxWordLength, excludeNouns, wordDelimiter);
    
    this.includeNamedEntities = includeNamedEntities;
    this.excludeEntityTags = excludeEntityTags;
    this.excludePosTags = excludePosTags;

    // Load SpaCy model (mock implementation)
    if (!includeNamedEntities) {
      this.nlp = this.loadSpacyModel(modelName, ['lemmatizer', 'ner']);
    } else {
      this.nlp = this.loadSpacyModel(modelName, ['lemmatizer']);
    }
  }

  /**
   * Extract noun phrases from text. Noun phrases may include named entities and noun chunks, 
   * which are filtered based on some heuristics.
   */
  extract(text: string): string[] {
    const doc = this.nlp(text);
    const filteredNounPhrases = new Set<string>();

    if (this.includeNamedEntities) {
      // Extract noun chunks + entities then filter overlapping spans
      const entities = doc.ents.filter(ent => 
        !this.excludeEntityTags.includes(ent.label_ || '')
      );
      
      let spans = [...entities, ...doc.noun_chunks];
      spans = this.filterSpans(spans);

      // Reading missing entities
      const missingEntities = entities.filter(ent =>
        !spans.some(span => ent.text === span.text || span.text.includes(ent.text))
      );
      spans.push(...missingEntities);

      // Filtering noun phrases based on some heuristics
      const taggedNounPhrases = spans.map(span => this.tagNounPhrases(span, entities));
      
      for (const taggedNp of taggedNounPhrases) {
        if (taggedNp.isValidEntity || 
            ((taggedNp.cleanedTokens.length > 1 || taggedNp.hasCompoundWords) && 
             taggedNp.hasValidTokens)) {
          filteredNounPhrases.add(taggedNp.cleanedText);
        }
      }
    } else {
      const taggedNounPhrases = doc.noun_chunks.map(chunk => 
        this.tagNounPhrases(chunk, [])
      );
      
      for (const taggedNp of taggedNounPhrases) {
        if (taggedNp.hasProperNouns || 
            ((taggedNp.cleanedTokens.length > 1 || taggedNp.hasCompoundWords) && 
             taggedNp.hasValidTokens)) {
          filteredNounPhrases.add(taggedNp.cleanedText);
        }
      }
    }

    return Array.from(filteredNounPhrases);
  }

  /**
   * Extract attributes of a noun chunk, to be used for filtering.
   */
  private tagNounPhrases(
    nounChunk: SpacySpan, 
    entities: SpacySpan[]
  ): {
    cleanedTokens: SpacyToken[];
    cleanedText: string;
    isValidEntity: boolean;
    hasProperNouns: boolean;
    hasCompoundWords: boolean;
    hasValidTokens: boolean;
  } {
    // Mock token extraction from span
    const tokens = this.getTokensFromSpan(nounChunk);
    
    const cleanedTokens = tokens.filter(token =>
      !this.excludePosTags.includes(token.pos_) &&
      !this.excludeNouns.includes(token.text.toUpperCase()) &&
      !token.is_space &&
      !token.is_punct
    );

    const cleanedTokenTexts = cleanedTokens.map(token => token.text);
    const cleanedTextString = cleanedTokenTexts
      .join(this.wordDelimiter)
      .replace(/\n/g, '')
      .toUpperCase();

    const nounChunkEntityLabels = entities
      .filter(ent => nounChunk.text === ent.text)
      .map(ent => [ent.text, ent.label_ || ''] as [string, string]);
    
    let validEntity = false;
    if (nounChunkEntityLabels.length > 0) {
      const nounChunkEntityLabel = nounChunkEntityLabels[0];
      validEntity = isValidEntity(nounChunkEntityLabel, cleanedTokenTexts);
    }

    return {
      cleanedTokens,
      cleanedText: cleanedTextString,
      isValidEntity: validEntity,
      hasProperNouns: cleanedTokens.some(token => token.pos_ === 'PROPN'),
      hasCompoundWords: isCompound(cleanedTokenTexts),
      hasValidTokens: hasValidTokenLength(cleanedTokenTexts, this.maxWordLength),
    };
  }

  /**
   * Return string representation of the extractor, used for cache key generation.
   */
  toString(): string {
    return `syntactic_${this.modelName}_${this.maxWordLength}_${this.includeNamedEntities}_${this.excludeEntityTags}_${this.excludePosTags}_${this.excludeNouns}_${this.wordDelimiter}`;
  }

  /**
   * Mock implementation of SpaCy model loading
   */
  private loadSpacyModel(modelName: string, exclude: string[]): SpacyNLP {
    // This is a mock implementation - in a real scenario, you'd integrate with a JS NLP library
    return (text: string): SpacyDoc => {
      // Mock implementation
      const words = text.split(/\s+/);
      return {
        ents: [], // Mock entities
        noun_chunks: words.map(word => ({ text: word })) // Mock noun chunks
      };
    };
  }

  /**
   * Filter overlapping spans (mock implementation)
   */
  private filterSpans(spans: SpacySpan[]): SpacySpan[] {
    // Simple mock implementation - in reality, this would be more sophisticated
    const seen = new Set<string>();
    return spans.filter(span => {
      if (seen.has(span.text)) {
        return false;
      }
      seen.add(span.text);
      return true;
    });
  }

  /**
   * Get tokens from a span (mock implementation)
   */
  private getTokensFromSpan(span: SpacySpan): SpacyToken[] {
    // Mock implementation - extract tokens from span text
    return span.text.split(/\s+/).map(word => ({
      text: word,
      pos_: 'NOUN', // Mock POS tag
      is_space: false,
      is_punct: /[^\w\s]/.test(word)
    }));
  }
}
