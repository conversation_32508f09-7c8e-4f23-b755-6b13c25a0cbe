// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * No-op Query Callbacks.
 */

import { QueryCallbacks, SearchResult } from './query-callbacks';

/**
 * A no-op implementation of QueryCallbacks.
 */
export class NoopQueryCallbacks implements QueryCallbacks {
    /**
     * Handle when context data is constructed.
     */
    onContext(context: any): void {
        // No-op implementation
    }

    /**
     * Handle the start of map operation.
     */
    onMapResponseStart(mapResponseContexts: string[]): void {
        // No-op implementation
    }

    /**
     * Handle the end of map operation.
     */
    onMapResponseEnd(mapResponseOutputs: SearchResult[]): void {
        // No-op implementation
    }

    /**
     * Handle the start of reduce operation.
     */
    onReduceResponseStart(reduceResponseContext: string | Record<string, any>): void {
        // No-op implementation
    }

    /**
     * Handle the end of reduce operation.
     */
    onReduceResponseEnd(reduceResponseOutput: string): void {
        // No-op implementation
    }

    /**
     * Handle when a new token is generated.
     */
    onLlmNewToken(token: string): void {
        // No-op implementation
    }
}