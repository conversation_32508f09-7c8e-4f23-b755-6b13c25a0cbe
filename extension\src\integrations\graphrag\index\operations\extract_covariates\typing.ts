/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'Covariate' and 'CovariateExtractionResult' models.
 */

import { PipelineCache } from '../../../cache/pipeline-cache';
import { WorkflowCallbacks } from '../../../callbacks/workflow-callbacks';

/**
 * Covariate class definition.
 */
export interface Covariate {
    covariateType?: string;
    subjectId?: string;
    objectId?: string;
    type?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    description?: string;
    sourceText?: string[];
    docId?: string;
    recordId?: number;
    id?: string;
}

/**
 * Covariate extraction result class definition.
 */
export interface CovariateExtractionResult {
    covariateData: Covariate[];
}

/**
 * Covariate extract strategy function type
 */
export type CovariateExtractStrategy = (
    input: Iterable<string>,
    entityTypes: string[],
    resolvedEntitiesMap: Record<string, string>,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    config: Record<string, any>
) => Promise<CovariateExtractionResult>;