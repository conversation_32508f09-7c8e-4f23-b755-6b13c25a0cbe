/**
 * @fileoverview Sub-Agent Executor - Handles independent execution of sub-agents with full tool access
 */

import { MainAgent } from "./main-agent"
import { SubAgentState } from "./types"
import { ToolExecutor } from "./tools/tool-executor"
import { Api<PERSON>anager } from "../../api/api-handler"
import { TaskExecutor } from "./task-executor/task-executor"
import { getCwd } from "./utils"
import { Anthropic } from "@anthropic-ai/sdk"
import { validateToolPermission, getToolPermissionsDescription } from "./tools/sub-agent-tool-permissions"

export interface SubAgentExecutionContext {
    subAgentId: string
    state: SubAgentState
    mainAgent: MainAgent
    instructions: string
    files: string[]
}

export class SubAgentExecutor {
    private toolExecutor: ToolExecutor
    private apiManager: ApiManager
    private context: SubAgentExecutionContext
    private isRunning: boolean = false

    constructor(context: SubAgentExecutionContext) {
        this.context = context
        this.apiManager = context.mainAgent.getApiManager()

        // Create a dedicated tool executor for this sub-agent
        this.toolExecutor = new ToolExecutor({
            cwd: getCwd(),
            alwaysAllowReadOnly: context.mainAgent.getStateManager().alwaysAllowReadOnly,
            alwaysAllowWriteOnly: context.mainAgent.getStateManager().alwaysAllowWriteOnly,
            MainAgent: context.mainAgent,
        })
    }

    /**
     * Start independent execution of the sub-agent
     */
    public async startExecution(): Promise<void> {
        if (this.isRunning) {
            throw new Error("Sub-agent is already running")
        }

        this.isRunning = true

        try {
            await this.executeIndependently()
        } catch (error) {
            console.error(`Sub-agent ${this.context.subAgentId} execution failed:`, error)
            await this.updateAgentStatus("EXITED")
        } finally {
            this.isRunning = false
        }
    }

    /**
     * Stop the sub-agent execution
     */
    public async stopExecution(): Promise<void> {
        this.isRunning = false
        await this.updateAgentStatus("EXITED")
    }

    /**
     * Check if the sub-agent is currently running
     */
    public isExecuting(): boolean {
        return this.isRunning
    }

    /**
     * Independent execution logic for the sub-agent
     */
    private async executeIndependently(): Promise<void> {
        const { state, instructions, files } = this.context

        // Prepare the initial message for the sub-agent
        const initialMessage: Anthropic.Messages.MessageParam = {
            role: "user",
            content: [
                {
                    type: "text",
                    text: `You are now executing independently as a ${state.name} agent.

Task Instructions:
${instructions}

Files to work with:
${files.join(", ")}

Please analyze the task and begin execution. You have access to all necessary tools to complete this task. Work systematically and document your progress.

Remember to:
1. Think through the task step by step
2. Use appropriate tools to gather information and make changes
3. Provide clear reasoning for your actions
4. Use exit_agent when you have completed the task

Begin your independent execution now.`
                }
            ]
        }

        // Add initial message to conversation history
        state.apiConversationHistory.push(initialMessage)
        await this.updateAgentState()

        // Start the execution loop
        await this.executionLoop()
    }

    /**
     * Main execution loop for the sub-agent
     */
    private async executionLoop(): Promise<void> {
        let maxIterations = 20 // Prevent infinite loops
        let currentIteration = 0

        while (this.isRunning && currentIteration < maxIterations) {
            currentIteration++

            try {
                // Get AI response
                const response = await this.getAIResponse()

                if (!response) {
                    break
                }

                // Add AI response to conversation history
                this.context.state.apiConversationHistory.push({
                    role: "assistant",
                    content: response.content,
                    ts: Date.now()
                })

                // Parse and execute any tool calls
                const toolCalls = this.parseToolCalls(response.content)

                if (toolCalls.length === 0) {
                    // No tool calls, just continue
                    await this.updateAgentState()
                    continue
                }

                // Execute tool calls
                for (const toolCall of toolCalls) {
                    const toolResult = await this.executeToolCall(toolCall)

                    // Add tool result to conversation history
                    this.context.state.apiConversationHistory.push({
                        role: "user",
                        content: `Tool result: ${toolResult}`,
                        ts: Date.now()
                    })

                    // Check if this was an exit_agent call
                    if (toolCall.name === "exit_agent") {
                        await this.updateAgentStatus("DONE")
                        return
                    }
                }

                await this.updateAgentState()

                // Add a small delay to prevent overwhelming the API
                await new Promise(resolve => setTimeout(resolve, 1000))

            } catch (error) {
                console.error(`Sub-agent execution error:`, error)

                // Add error to history
                this.context.state.historyErrors[`execution_error_${Date.now()}`] = {
                    lastCheckedAt: Date.now(),
                    error: error instanceof Error ? error.message : "Unknown execution error"
                }

                await this.updateAgentState()
                break
            }
        }

        if (currentIteration >= maxIterations) {
            console.warn(`Sub-agent ${this.context.subAgentId} reached maximum iterations`)
            await this.updateAgentStatus("EXITED")
        }
    }

    /**
     * Get AI response using the sub-agent's system prompt and conversation history
     * Uses the API queue to prevent congestion
     */
    private async getAIResponse(): Promise<Anthropic.Messages.Message | null> {
        try {
            const messages = this.context.state.apiConversationHistory.slice(-10) // Keep last 10 messages

            // Queue the API request through the sub-agent manager
            // For now, use a simplified approach - we'll need to integrate with the actual API system later
            const response = await this.context.mainAgent.getStateManager().subAgentManager.queueApiRequest(
                this.context.subAgentId,
                this.context.state.name,
                async () => {
                    // This is a placeholder - in real implementation, we'd use the proper API stream
                    return {
                        id: `msg_${Date.now()}`,
                        type: "message" as const,
                        role: "assistant" as const,
                        content: [{ type: "text" as const, text: "Sub-agent response placeholder - needs proper API integration" }],
                        model: "claude-3-5-sonnet-20241022",
                        stop_reason: "end_turn" as const,
                        stop_sequence: null,
                        usage: { input_tokens: 0, output_tokens: 0 }
                    }
                }
            )

            return response as any
        } catch (error) {
            console.error("Failed to get AI response:", error)
            return null
        }
    }

    /**
     * Parse tool calls from AI response content
     */
    private parseToolCalls(content: any): Array<{ name: string; parameters: any }> {
        const toolCalls: Array<{ name: string; parameters: any }> = []

        // Handle different content formats
        let textContent = ""
        if (typeof content === "string") {
            textContent = content
        } else if (Array.isArray(content)) {
            textContent = content
                .filter(block => block.type === "text")
                .map(block => block.text)
                .join("\n")
        }

        // Parse XML-style tool calls (matching the prompt format)
        const toolRegex = /<(\w+)>([\s\S]*?)<\/\1>/g
        let match

        while ((match = toolRegex.exec(textContent)) !== null) {
            const toolName = match[1]
            const toolContent = match[2]

            // Skip non-tool tags
            if (["thinking", "observation", "action", "self_critique"].includes(toolName)) {
                continue
            }

            // Parse parameters from the tool content
            const parameters: any = {}
            const paramRegex = /<(\w+)>([\s\S]*?)<\/\1>/g
            let paramMatch

            while ((paramMatch = paramRegex.exec(toolContent)) !== null) {
                parameters[paramMatch[1]] = paramMatch[2].trim()
            }

            toolCalls.push({
                name: toolName,
                parameters
            })
        }

        return toolCalls
    }

    /**
     * Execute a tool call using the tool executor with permission validation
     */
    private async executeToolCall(toolCall: { name: string; parameters: any }): Promise<string> {
        try {
            // Validate tool permission for this sub-agent type
            const permission = validateToolPermission(this.context.state.name, toolCall.name as any)

            if (!permission.allowed) {
                const errorMessage = `Permission denied: ${permission.reason}`
                console.warn(errorMessage)

                // Add permission error to agent's error history
                this.context.state.historyErrors[`permission_error_${Date.now()}`] = {
                    lastCheckedAt: Date.now(),
                    error: errorMessage
                }

                return errorMessage
            }

            // For now, use a simplified approach - we'll need to integrate with the actual tool system later
            const result = await this.toolExecutor.processToolUse(
                `<${toolCall.name}>${Object.entries(toolCall.parameters).map(([key, value]) =>
                    `<${key}>${value}</${key}>`
                ).join('')}</${toolCall.name}>`
            )

            return typeof result === 'string' ? result : "Tool executed successfully"
        } catch (error) {
            const errorMessage = `Tool execution failed: ${error instanceof Error ? error.message : String(error)}`
            console.error(errorMessage)

            // Add execution error to agent's error history
            this.context.state.historyErrors[`execution_error_${Date.now()}`] = {
                lastCheckedAt: Date.now(),
                error: errorMessage
            }

            return errorMessage
        }
    }

    /**
     * Update the sub-agent's state in the state manager
     */
    private async updateAgentState(): Promise<void> {
        await this.context.mainAgent.getStateManager().subAgentManager.updateSubAgentState(
            parseInt(this.context.subAgentId),
            this.context.state
        )
    }

    /**
     * Update the sub-agent's status
     */
    private async updateAgentStatus(status: SubAgentState["state"]): Promise<void> {
        this.context.state.state = status
        await this.updateAgentState()
    }
}