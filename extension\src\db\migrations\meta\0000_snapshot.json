{"version": "6", "dialect": "sqlite", "id": "6fcd5e17-a322-4e8c-83e0-e4dd8092f187", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"ai_responses": {"name": "ai_responses", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "task_request_id": {"name": "task_request_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "agent_id": {"name": "agent_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "agent_name": {"name": "agent_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contents": {"name": "contents", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "images": {"name": "images", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "input_tokens": {"name": "input_tokens", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "output_tokens": {"name": "output_tokens", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "input_cache_read": {"name": "input_cache_read", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "input_cache_write": {"name": "input_cache_write", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_error": {"name": "is_error", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_text": {"name": "error_text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"ai_responses_task_request_id_task_requests_id_fk": {"name": "ai_responses_task_request_id_task_requests_id_fk", "tableFrom": "ai_responses", "tableTo": "task_requests", "columnsFrom": ["task_request_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "human_messages": {"name": "human_messages", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "task_request_id": {"name": "task_request_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_name": {"name": "user_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contents": {"name": "contents", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "images": {"name": "images", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"human_messages_task_request_id_task_requests_id_fk": {"name": "human_messages_task_request_id_task_requests_id_fk", "tableFrom": "human_messages", "tableTo": "task_requests", "columnsFrom": ["task_request_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "prompt_templates": {"name": "prompt_templates", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "agent_name": {"name": "agent_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "enabled_tools": {"name": "enabled_tools", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"prompt_templates_name_unique": {"name": "prompt_templates_name_unique", "columns": ["name"], "isUnique": true}, "prompt_templates_name_idx": {"name": "prompt_templates_name_idx", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "request_tools": {"name": "request_tools", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "ai_response_id": {"name": "ai_response_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "tool_id": {"name": "tool_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tool_name": {"name": "tool_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "params": {"name": "params", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tool_status": {"name": "tool_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tool_feedback": {"name": "tool_feedback", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"request_tools_ai_response_id_ai_responses_id_fk": {"name": "request_tools_ai_response_id_ai_responses_id_fk", "tableFrom": "request_tools", "tableTo": "ai_responses", "columnsFrom": ["ai_response_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "task_agents": {"name": "task_agents", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "is_main_thread": {"name": "is_main_thread", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "agent_id": {"name": "agent_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "agent_name": {"name": "agent_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "task_id": {"name": "task_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"task_agents_task_id_tasks_id_fk": {"name": "task_agents_task_id_tasks_id_fk", "tableFrom": "task_agents", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "task_files": {"name": "task_files", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "task_id": {"name": "task_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_version": {"name": "file_version", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"task_files_task_id_tasks_id_fk": {"name": "task_files_task_id_tasks_id_fk", "tableFrom": "task_files", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "task_requests": {"name": "task_requests", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "task_id": {"name": "task_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "agent_id": {"name": "agent_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_aborted": {"name": "is_aborted", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "request_started_at": {"name": "request_started_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "request_ended_at": {"name": "request_ended_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"task_requests_task_id_tasks_id_fk": {"name": "task_requests_task_id_tasks_id_fk", "tableFrom": "task_requests", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "task_requests_agent_id_task_agents_agent_id_fk": {"name": "task_requests_agent_id_task_agents_agent_id_fk", "tableFrom": "task_requests", "tableTo": "task_agents", "columnsFrom": ["agent_id"], "columnsTo": ["agent_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tasks": {"name": "tasks", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}