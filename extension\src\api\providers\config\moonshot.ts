// providers/moonshot.ts
import { ProviderConfig } from "../types"
import { DEFAULT_BASE_URLS, PROVIDER_IDS, PROVIDER_NAMES } from "../constants"

export const moonshotConfig: ProviderConfig = {
    id: PROVIDER_IDS.MOONSHOT,
    name: PROVIDER_NAMES[PROVIDER_IDS.MOONSHOT],
    baseUrl: DEFAULT_BASE_URLS[PROVIDER_IDS.MOONSHOT],
    models: [
        {
            id: "kimi-k2-0711-preview",
            name: "<PERSON><PERSON> K2",
            contextWindow: 128000,
            maxTokens: 4096,
            supportsImages: false,
            supportsPromptCache: false,
            inputPrice: 0.14, // ￥1.00 converted to USD at ~7.2 exchange rate
            outputPrice: 2.22, // ￥16.00 converted to USD at ~7.2 exchange rate
            provider: PROVIDER_IDS.MOONSHOT,
            isRecommended: true,
        },
        {
            id: "kimi-k2-turbo-preview",
            name: "<PERSON><PERSON> K2 Turbo",
            contextWindow: 128000,
            maxTokens: 4096,
            supportsImages: false,
            supportsPromptCache: false,
            inputPrice: 0.14, // ￥1.00 converted to USD at ~7.2 exchange rate
            outputPrice: 2.22, // ￥16.00 converted to USD at ~7.2 exchange rate
            provider: PROVIDER_IDS.MOONSHOT,
        },
        {
            id: "moonshot-v1-128k-vision-preview",
            name: "Kimi Vision 128K",
            contextWindow: 131072, // 128K context window, each image consumes 1024 tokens
            maxTokens: 4096,
            supportsImages: true,
            supportsPromptCache: false,
            inputPrice: 0.14, // ￥1.00 converted to USD at ~7.2 exchange rate
            outputPrice: 2.22, // ￥16.00 converted to USD at ~7.2 exchange rate
            provider: PROVIDER_IDS.MOONSHOT,
            isRecommended: true,
        },
    ],
    requiredFields: ["apiKey"],
}