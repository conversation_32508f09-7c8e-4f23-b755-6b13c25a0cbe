// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing run and _embed_text methods definitions.
 */

import { PipelineCache } from '../../../../cache/pipeline-cache.js';
import { WorkflowCallbacks } from '../../../../callbacks/workflow-callbacks.js';
import { TextEmbeddingResult } from './types.js';
import { ProgressTicker, progressTicker } from '../../../../logger/progress.js';

/**
 * Run the mock text embedding strategy.
 */
export async function run(
  input: string[],
  callbacks: WorkflowCallbacks,
  cache: PipelineCache,
  _args: Record<string, any>
): Promise<TextEmbeddingResult> {
  const inputArray = Array.isArray(input) ? input : [input];
  const ticker = progressTicker(
    callbacks.progress,
    inputArray.length,
    'generate embeddings progress: '
  );

  return {
    embeddings: inputArray.map(text => embedText(cache, text, ticker))
  };
}

/**
 * Embed a single piece of text using mock random values.
 */
function embedText(_cache: PipelineCache, _text: string, tick: ProgressTicker): number[] {
  tick(1);
  return [Math.random(), Math.random(), Math.random()];
}
