// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Sort local context by total degree of associated nodes in descending order.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';
import { numTokens } from '../../../../query/llm/text-utils.js';

/**
 * Concatenate structured data into a context string.
 */
export function getContextString(
  textUnits: Record<string, any>[],
  subCommunityReports?: Record<string, any>[]
): string {
  const contexts: string[] = [];
  
  if (subCommunityReports) {
    const filteredReports = subCommunityReports.filter(report =>
      schemas.COMMUNITY_ID in report &&
      report[schemas.COMMUNITY_ID] &&
      String(report[schemas.COMMUNITY_ID]).trim() !== ''
    );
    
    const reportDf = DataFrame.fromRecords(filteredReports).dropDuplicates();
    if (!reportDf.isEmpty()) {
      // Convert community ID to int if it's float
      let processedDf = reportDf;
      const communityIdColumn = processedDf.getColumn(schemas.COMMUNITY_ID);
      if (communityIdColumn.some(id => typeof id === 'number' && id % 1 !== 0)) {
        processedDf = processedDf.withColumn(
          schemas.COMMUNITY_ID,
          communityIdColumn.map(id => Math.floor(Number(id)))
        );
      }
      
      const reportString = `----REPORTS-----\n${processedDf.toCsv()}`;
      contexts.push(reportString);
    }
  }

  const filteredTextUnits = textUnits.filter(unit =>
    'id' in unit && unit.id && String(unit.id).trim() !== ''
  );
  
  const textUnitsDf = DataFrame.fromRecords(filteredTextUnits).dropDuplicates();
  if (!textUnitsDf.isEmpty()) {
    // Convert id to int if it's float
    let processedDf = textUnitsDf;
    const idColumn = processedDf.getColumn('id');
    if (idColumn.some(id => typeof id === 'number' && id % 1 !== 0)) {
      processedDf = processedDf.withColumn(
        'id',
        idColumn.map(id => Math.floor(Number(id)))
      );
    }
    
    const textUnitString = `-----SOURCES-----\n${processedDf.toCsv()}`;
    contexts.push(textUnitString);
  }

  return contexts.join('\n\n');
}

/**
 * Sort local context (list of text units) by total degree of associated nodes in descending order.
 */
export function sortContext(
  localContext: Record<string, any>[],
  subCommunityReports?: Record<string, any>[],
  maxContextTokens?: number
): string {
  // Sort text units by entity degree in descending order
  const sortedTextUnits = [...localContext].sort((a, b) => {
    const degreeA = a[schemas.ENTITY_DEGREE] || 0;
    const degreeB = b[schemas.ENTITY_DEGREE] || 0;
    return degreeB - degreeA;
  });

  const currentTextUnits: Record<string, any>[] = [];
  let contextString = '';
  
  for (const record of sortedTextUnits) {
    currentTextUnits.push(record);
    
    if (maxContextTokens) {
      const newContextString = getContextString(currentTextUnits, subCommunityReports);
      if (numTokens(newContextString) > maxContextTokens) {
        break;
      }
      contextString = newContextString;
    }
  }

  if (contextString === '') {
    return getContextString(sortedTextUnits, subCommunityReports);
  }

  return contextString;
}
