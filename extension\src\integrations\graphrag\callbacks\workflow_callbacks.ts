// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Collection of callbacks that can be used to monitor the workflow execution.
 */

import { PipelineRunResult } from '../index/typing/pipeline-run-result';
import { Progress } from '../logger/progress';

/**
 * A collection of callbacks that can be used to monitor the workflow execution.
 * 
 * This base class is a "noop" implementation so that clients may implement just the callbacks they need.
 */
export interface WorkflowCallbacks {
    /**
     * Execute this callback to signal when the entire pipeline starts.
     */
    pipelineStart(names: string[]): void;

    /**
     * Execute this callback to signal when the entire pipeline ends.
     */
    pipelineEnd(results: PipelineRunResult[]): void;

    /**
     * Execute this callback when a workflow starts.
     */
    workflowStart(name: string, instance: object): void;

    /**
     * Execute this callback when a workflow ends.
     */
    workflowEnd(name: string, instance: object): void;

    /**
     * Handle when progress occurs.
     */
    progress(progress: Progress): void;
}