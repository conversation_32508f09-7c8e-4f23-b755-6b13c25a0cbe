/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run and _create_node_position methods definitions.
 */

import { Graph } from '../../utils/graphs';
import { GraphLayout, NodePosition } from './typing';
import { ErrorHandlerFn } from '../../typing/error-handler';

const logger = console;

/**
 * Run zero-position layout algorithm (places all nodes at origin).
 */
export function run(
    graph: Graph,
    onError: ErrorHandlerFn
): GraphLayout {
    const nodeClusters: number[] = [];
    const nodeSizes: number[] = [];

    const nodes = Array.from(graph.nodes.keys());

    for (const nodeId of nodes) {
        const node = graph.nodes.get(nodeId) || {};
        const cluster = node.cluster || node.community || -1;
        nodeClusters.push(cluster);
        const size = node.degree || node.size || 0;
        nodeSizes.push(size);
    }

    const additionalArgs: any = {};
    if (nodeClusters.length > 0) {
        additionalArgs.nodeCategories = nodeClusters;
    }
    if (nodeSizes.length > 0) {
        additionalArgs.nodeSizes = nodeSizes;
    }

    try {
        return getZeroPositions(
            nodes,
            additionalArgs.nodeCategories,
            additionalArgs.nodeSizes
        );
    } catch (e) {
        const error = e instanceof Error ? e : new Error(String(e));
        logger.error('Error running zero-position', error);
        onError(error, error.stack || '', null);
        
        // Return fallback layout with all nodes at (0, 0)
        const result: NodePosition[] = [];
        for (let i = 0; i < nodes.length; i++) {
            const cluster = nodeClusters.length > 0 ? nodeClusters[i] : 1;
            result.push({
                x: 0,
                y: 0,
                label: nodes[i],
                size: 0,
                cluster: String(cluster)
            });
        }
        return result;
    }
}

/**
 * Get zero positions for all nodes (places all nodes at origin).
 */
function getZeroPositions(
    nodeLabels: string[],
    nodeCategories?: number[],
    nodeSizes?: number[],
    threeD: boolean = false
): NodePosition[] {
    const embeddingPositionData: NodePosition[] = [];
    
    for (let index = 0; index < nodeLabels.length; index++) {
        const nodeName = nodeLabels[index];
        const nodeCategory = nodeCategories ? nodeCategories[index] : 1;
        const nodeSize = nodeSizes ? nodeSizes[index] : 1;

        if (!threeD) {
            embeddingPositionData.push({
                label: String(nodeName),
                x: 0,
                y: 0,
                cluster: String(Math.floor(nodeCategory)),
                size: Math.floor(nodeSize)
            });
        } else {
            embeddingPositionData.push({
                label: String(nodeName),
                x: 0,
                y: 0,
                z: 0,
                cluster: String(Math.floor(nodeCategory)),
                size: Math.floor(nodeSize)
            });
        }
    }
    
    return embeddingPositionData;
}