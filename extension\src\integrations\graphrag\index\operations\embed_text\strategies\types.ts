// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing 'TextEmbeddingResult' model.
 */

import { PipelineCache } from '../../../../cache/pipeline-cache.js';
import { WorkflowCallbacks } from '../../../../callbacks/workflow-callbacks.js';

/**
 * Text embedding result class definition.
 */
export interface TextEmbeddingResult {
  embeddings?: (number[] | undefined)[];
}

/**
 * Text embedding strategy function type.
 */
export type TextEmbeddingStrategy = (
  input: string[],
  callbacks: WorkflowCallbacks,
  cache: PipelineCache,
  args: Record<string, any>
) => Promise<TextEmbeddingResult>;
