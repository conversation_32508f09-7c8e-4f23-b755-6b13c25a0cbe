import { SpawnAgentOptions } from "../tools/schema/agents/agent-spawner"
import { SubAgentState } from "../types"
import { IOManager } from "./io-manager"
import { TaskConflictManager, TaskScope } from "../conflict-resolution/task-conflict-manager"
import { ApiRequestQueue } from "../api-queue/api-request-queue"
import { GitRepositoryManager } from "../git-manager/git-repository-manager"
import { AgentCommunicationProtocol, AgentCard, A2AMessage } from "../a2a-communication/agent-communication-protocol"
import path from "path"
import fs from "fs/promises"

type SubAgentManagerOptions = {
	ioManager: IOManager
	subAgentId?: number
	onEnterSucessful: (state: SubAgentState) => Promise<void>
	onExit: () => Promise<void>
	cwd?: string
}

export interface TaskListItem {
	id: string
	title: string
	description: string
	priority: "high" | "medium" | "low"
	status: "pending" | "in_progress" | "completed" | "failed"
	assignedAgent?: SpawnAgentOptions
	createdAt: number
	updatedAt: number
	dependencies?: string[]
	estimatedTime?: number
}

export interface TaskSummaryReport {
	taskId: string
	agentName: SpawnAgentOptions
	status: "completed" | "failed" | "partial"
	summary: string
	achievements: string[]
	challenges: string[]
	recommendations: string[]
	filesModified: string[]
	timeSpent: number
	createdAt: number
}

export class SubAgentManager {
	private _ioManager: IOManager
	private _state?: SubAgentState
	private _currentSubAgentId?: number
	private _agentHash?: string
	private onEnterSucessful: (state: SubAgentState) => Promise<void>
	private onExit: () => Promise<void>
	private _cwd: string
	private _activeAgents: Map<string, SubAgentState> = new Map()
	private _agentSnapshots: Map<string, string> = new Map() // agentId -> snapshotId
	private _conflictManager: TaskConflictManager
	private _apiQueue: ApiRequestQueue
	private _gitManager: GitRepositoryManager
	private _a2aProtocol: AgentCommunicationProtocol
	private _isGitRequired: boolean = true

	constructor(options: SubAgentManagerOptions) {
		this._ioManager = options.ioManager
		this.onEnterSucessful = options.onEnterSucessful
		this.onExit = options.onExit
		this._cwd = options.cwd || process.cwd()
		
		// Initialize managers
		this._conflictManager = new TaskConflictManager()
		this._apiQueue = new ApiRequestQueue({
			concurrency: 3,
			intervalCap: 15,
			interval: 60000
		})
		this._gitManager = new GitRepositoryManager(this._cwd)
		this._a2aProtocol = new AgentCommunicationProtocol()
		
		// Initialize A2A agent cards
		this.initializeAgentCards()
		
		if (options.subAgentId) {
			this.enterSubAgent(options.subAgentId)
		}
	}

	get state(): SubAgentState | undefined {
		return this._state
	}

	get agentName(): SpawnAgentOptions | undefined {
		return this._state?.name
	}

	get agentHash(): string | undefined {
		return this._agentHash
	}

	get currentSubAgentId(): number | undefined {
		return this._currentSubAgentId
	}

	get isInSubAgent(): boolean {
		return this._currentSubAgentId !== undefined
	}

	public async exitSubAgent(): Promise<void> {
		// first save the state
		if (this._state) {
			await this._ioManager.saveSubAgentState(this._state)
		}
		// Clear state in correct order
		this._currentSubAgentId = undefined
		this._state = undefined
		this._ioManager.agentHash = undefined
		await this.onExit()
	}

	public getHash(): string {
		if (!this._state) {
			throw new Error("No current sub-agent state")
		}
		return `${this._state.ts}-${this._state.name}`
	}

	public async updateSubAgentState(subAgentId: number, state: SubAgentState): Promise<void> {
		if (!this._state) {
			throw new Error(`SubAgent with id ${subAgentId} does not exist`)
		}
		Object.assign(this._state, state)
		await this._ioManager.saveSubAgentState(this._state)
	}

	public async enterSubAgent(subAgentId: number): Promise<void> {
		this._currentSubAgentId = subAgentId
		this._ioManager.agentHash = this.getHash()

		const state = await this._ioManager.loadSubAgentState()
		if (state) {
			this._state = state
			await this.onEnterSucessful(state)
			return
		}
		// if not we exit and throw an error
		this.exitSubAgent()
		throw new Error(`SubAgent with id ${subAgentId} does not exist`)
	}

	public async spawnSubAgent(subAgentId: number, subAgentState: SubAgentState): Promise<void> {
		// Check git repository requirement
		if (this._isGitRequired) {
			const isGitRepo = await this._gitManager.isGitRepository()
			if (!isGitRepo) {
				throw new Error("Git repository required for sub-agent system. Please initialize git repository first.")
			}
		}

		this._currentSubAgentId = subAgentId
		this._state = subAgentState
		this._ioManager.agentHash = this.getHash()
		await this._ioManager.saveSubAgentState(subAgentState)
		
		// Create git snapshot before agent execution
		try {
			const snapshotId = await this._gitManager.createSnapshot(
				subAgentState.ts.toString(),
				`Pre-execution snapshot for ${subAgentState.name} agent`
			)
			this._agentSnapshots.set(subAgentState.ts.toString(), snapshotId)
		} catch (error) {
			console.warn("Failed to create git snapshot:", error)
		}
		
		// Add to active agents map for independent tracking
		this._activeAgents.set(subAgentState.ts.toString(), subAgentState)
		
		// Register task scope for conflict detection
		this.registerAgentTaskScope(subAgentState)
		
		await this.onEnterSucessful(subAgentState)
	}

	// New methods for A2A architecture
	public async generateTaskList(mainTask: string, context: string): Promise<TaskListItem[]> {
		const taskList: TaskListItem[] = []
		const timestamp = Date.now()
		
		// Generate task breakdown based on main task
		// This would typically use AI to analyze and break down the task
		const tasks = await this.analyzeAndBreakdownTask(mainTask, context)
		
		for (let i = 0; i < tasks.length; i++) {
			const task = tasks[i]
			taskList.push({
				id: `task_${timestamp}_${i}`,
				title: task.title,
				description: task.description,
				priority: task.priority || "medium",
				status: "pending",
				assignedAgent: task.suggestedAgent,
				createdAt: timestamp,
				updatedAt: timestamp,
				dependencies: task.dependencies,
				estimatedTime: task.estimatedTime
			})
		}

		// Save task list to .Ai directory
		await this.saveTaskListToFile(taskList)
		return taskList
	}

	public async generateTaskSummaryReport(agentId: string, taskResult: any): Promise<TaskSummaryReport> {
		const agent = this._activeAgents.get(agentId)
		if (!agent) {
			throw new Error(`Agent ${agentId} not found`)
		}

		const report: TaskSummaryReport = {
			taskId: agentId,
			agentName: agent.name,
			status: taskResult.success ? "completed" : "failed",
			summary: taskResult.summary || "Task execution completed",
			achievements: taskResult.achievements || [],
			challenges: taskResult.challenges || [],
			recommendations: taskResult.recommendations || [],
			filesModified: taskResult.filesModified || [],
			timeSpent: Date.now() - agent.ts,
			createdAt: Date.now()
		}

		// Save report to .Ai directory
		await this.saveTaskSummaryToFile(report)
		return report
	}

	public getActiveAgents(): Map<string, SubAgentState> {
		return this._activeAgents
	}

	public async updateAgentStatus(agentId: string, status: SubAgentState["state"]): Promise<void> {
		const agent = this._activeAgents.get(agentId)
		if (agent) {
			agent.state = status
			this._activeAgents.set(agentId, agent)
			await this._ioManager.saveSubAgentState(agent)
		}
	}

	private async analyzeAndBreakdownTask(mainTask: string, context: string): Promise<any[]> {
		// This would typically use AI to analyze the task
		// For now, return a simple breakdown
		return [
			{
				title: "Analysis Phase",
				description: "Analyze the requirements and understand the scope",
				priority: "high",
				suggestedAgent: "planner",
				estimatedTime: 300000 // 5 minutes
			},
			{
				title: "Implementation Phase", 
				description: "Implement the core functionality",
				priority: "high",
				suggestedAgent: "sub_task",
				dependencies: ["task_0"],
				estimatedTime: 900000 // 15 minutes
			},
			{
				title: "Testing Phase",
				description: "Test and validate the implementation",
				priority: "medium", 
				suggestedAgent: "sub_task",
				dependencies: ["task_1"],
				estimatedTime: 600000 // 10 minutes
			}
		]
	}

	private async ensureAiDirectory(): Promise<string> {
		const aiDir = path.join(this._cwd, ".Ai")
		await fs.mkdir(aiDir, { recursive: true })
		return aiDir
	}

	private async saveTaskListToFile(taskList: TaskListItem[]): Promise<void> {
		const aiDir = await this.ensureAiDirectory()
		const filePath = path.join(aiDir, "task-list.md")
		
		let content = "# Task List\n\n"
		content += `Generated at: ${new Date().toISOString()}\n\n`
		
		for (const task of taskList) {
			content += `## ${task.title}\n\n`
			content += `- **ID**: ${task.id}\n`
			content += `- **Description**: ${task.description}\n`
			content += `- **Priority**: ${task.priority}\n`
			content += `- **Status**: ${task.status}\n`
			content += `- **Assigned Agent**: ${task.assignedAgent || "unassigned"}\n`
			content += `- **Estimated Time**: ${task.estimatedTime ? Math.round(task.estimatedTime / 60000) + " minutes" : "unknown"}\n`
			if (task.dependencies && task.dependencies.length > 0) {
				content += `- **Dependencies**: ${task.dependencies.join(", ")}\n`
			}
			content += "\n"
		}

		await fs.writeFile(filePath, content, "utf8")
	}

	private async saveTaskSummaryToFile(report: TaskSummaryReport): Promise<void> {
		const aiDir = await this.ensureAiDirectory()
		const fileName = `task-summary-${report.taskId}-${Date.now()}.md`
		const filePath = path.join(aiDir, fileName)
		
		let content = `# Task Summary Report\n\n`
		content += `**Task ID**: ${report.taskId}\n`
		content += `**Agent**: ${report.agentName}\n`
		content += `**Status**: ${report.status}\n`
		content += `**Time Spent**: ${Math.round(report.timeSpent / 60000)} minutes\n`
		content += `**Generated**: ${new Date(report.createdAt).toISOString()}\n\n`
		
		content += `## Summary\n\n${report.summary}\n\n`
		
		if (report.achievements.length > 0) {
			content += `## Achievements\n\n`
			for (const achievement of report.achievements) {
				content += `- ${achievement}\n`
			}
			content += "\n"
		}
		
		if (report.challenges.length > 0) {
			content += `## Challenges\n\n`
			for (const challenge of report.challenges) {
				content += `- ${challenge}\n`
			}
			content += "\n"
		}
		
		if (report.recommendations.length > 0) {
			content += `## Recommendations\n\n`
			for (const recommendation of report.recommendations) {
				content += `- ${recommendation}\n`
			}
			content += "\n"
		}
		
		if (report.filesModified.length > 0) {
			content += `## Files Modified\n\n`
			for (const file of report.filesModified) {
				content += `- ${file}\n`
			}
			content += "\n"
		}

		await fs.writeFile(filePath, content, "utf8")
	}

	// New methods for enhanced functionality

	/**
	 * Terminate agent and revert changes
	 */
	public async terminateAgentAndRevert(agentId: string): Promise<void> {
		const agent = this._activeAgents.get(agentId)
		if (!agent) {
			throw new Error(`Agent ${agentId} not found`)
		}

		// Update agent status to exited
		agent.state = "EXITED"
		this._activeAgents.set(agentId, agent)

		// Revert git changes if snapshot exists
		const snapshotId = this._agentSnapshots.get(agentId)
		if (snapshotId) {
			try {
				await this._gitManager.revertToSnapshot(snapshotId)
				console.log(`Reverted changes for agent ${agentId}`)
			} catch (error) {
				console.error(`Failed to revert changes for agent ${agentId}:`, error)
			}
			this._agentSnapshots.delete(agentId)
		}

		// Cancel pending API requests
		this._apiQueue.cancelAgentRequests(agentId)

		// Unregister from conflict manager
		this._conflictManager.unregisterTaskScope(agentId)

		// Remove from active agents
		this._activeAgents.delete(agentId)
	}

	/**
	 * Register agent task scope for conflict detection
	 */
	private registerAgentTaskScope(agent: SubAgentState): void {
		const scope: TaskScope = {
			agentId: agent.ts.toString(),
			agentType: agent.name,
			assignedFiles: [], // Would be populated from agent instructions
			assignedDirectories: [],
			taskDescription: agent.automaticReminders || "",
			priority: this.getAgentPriority(agent.name),
			dependencies: [],
			exclusiveResources: []
		}

		this._conflictManager.registerTaskScope(scope)
	}

	/**
	 * Get agent priority for conflict resolution
	 */
	private getAgentPriority(agentType: SpawnAgentOptions): number {
		const priorities = {
			"planner": 10,
			"analyzer": 8,
			"researcher": 6,
			"coder": 4,
			"sub_task": 2
		}
		return priorities[agentType] || 1
	}

	/**
	 * Queue API request for agent
	 */
	public async queueApiRequest<T>(
		agentId: string,
		agentType: string,
		request: () => Promise<T>
	): Promise<T> {
		const priority = this._apiQueue.getAgentPriority(agentType)
		return await this._apiQueue.enqueueRequest(agentId, agentType, request, priority)
	}

	/**
	 * Check if git repository is initialized
	 */
	public async checkGitRepository(): Promise<{
		isInitialized: boolean
		canInitialize: boolean
		message: string
	}> {
		const isRepo = await this._gitManager.isGitRepository()
		
		if (isRepo) {
			return {
				isInitialized: true,
				canInitialize: false,
				message: "Git repository is already initialized"
			}
		}

		return {
			isInitialized: false,
			canInitialize: true,
			message: "Git repository is required for sub-agent system. Click to initialize."
		}
	}

	/**
	 * Initialize git repository
	 */
	public async initializeGitRepository(): Promise<void> {
		await this._gitManager.initializeRepository()
	}

	/**
	 * Get conflict information
	 */
	public getActiveConflicts() {
		return this._conflictManager.getActiveConflicts()
	}

	/**
	 * Get API queue statistics
	 */
	public getApiQueueStats() {
		return this._apiQueue.getStats()
	}

	/**
	 * Get git repository info
	 */
	public async getGitRepositoryInfo() {
		return await this._gitManager.getRepositoryInfo()
	}

	/**
	 * Resolve conflicts
	 */
	public resolveConflicts(): void {
		this._conflictManager.resolveConflicts()
	}

	/**
	 * Check if agent can access resource
	 */
	public canAgentAccessResource(agentId: string, resource: string): boolean {
		return this._conflictManager.canAccessResource(agentId, resource)
	}

	/**
	 * Get agent execution summary
	 */
	public getAgentExecutionSummary(agentId: string): {
		agent?: SubAgentState
		conflicts: number
		apiRequests: any
		hasSnapshot: boolean
		gitChanges?: string[]
	} {
		const agent = this._activeAgents.get(agentId)
		const conflicts = this._conflictManager.getActiveConflicts()
			.filter(c => c.agents.includes(agentId))
		const apiRequests = this._apiQueue.getAgentStats(agentId)
		const hasSnapshot = this._agentSnapshots.has(agentId)

		return {
			agent,
			conflicts: conflicts.length,
			apiRequests,
			hasSnapshot,
			gitChanges: [] // Would be populated from git diff
		}
	}

	/**
	 * Disable git requirement (for testing)
	 */
	public setGitRequired(required: boolean): void {
		this._isGitRequired = required
	}

	/**
	 * Initialize A2A agent cards for all agent types
	 */
	private initializeAgentCards(): void {
		const agentCards: AgentCard[] = [
			{
				id: "planner",
				name: "Planner Agent",
				description: "Analyzes tasks and creates detailed execution plans",
				version: "1.0.0",
				capabilities: [
					{
						name: "task_planning",
						description: "Break down complex tasks into actionable steps",
						inputTypes: ["text", "data"],
						outputTypes: ["text", "document"]
					},
					{
						name: "analysis",
						description: "Analyze project structure and requirements",
						inputTypes: ["text", "file"],
						outputTypes: ["text", "analysis"]
					}
				],
				metadata: { priority: 10, type: "planning" }
			},
			{
				id: "researcher",
				name: "Researcher Agent",
				description: "Conducts research and provides high-value recommendations",
				version: "1.0.0",
				capabilities: [
					{
						name: "codebase_analysis",
						description: "Analyze codebase structure and patterns",
						inputTypes: ["file", "text"],
						outputTypes: ["analysis", "document"]
					},
					{
						name: "best_practices_research",
						description: "Research and recommend best practices",
						inputTypes: ["text", "data"],
						outputTypes: ["text", "analysis"]
					}
				],
				metadata: { priority: 6, type: "research" }
			},

			{
				id: "analyzer",
				name: "Analyzer Agent", 
				description: "Analyzes project architecture and provides insights",
				version: "1.0.0",
				capabilities: [
					{
						name: "architecture_analysis",
						description: "Analyze project architecture and dependencies",
						inputTypes: ["file", "text"],
						outputTypes: ["analysis", "document"]
					},
					{
						name: "code_quality_assessment",
						description: "Assess code quality and maintainability",
						inputTypes: ["file", "text"],
						outputTypes: ["analysis", "document"]
					}
				],
				metadata: { priority: 8, type: "analysis" }
			},
			{
				id: "coder",
				name: "Coder Agent",
				description: "Handles general coding and development tasks",
				version: "1.0.0",
				capabilities: [
					{
						name: "code_implementation",
						description: "Implement features and fix bugs",
						inputTypes: ["text", "file"],
						outputTypes: ["code", "file"]
					},
					{
						name: "refactoring",
						description: "Refactor and optimize existing code",
						inputTypes: ["file", "text"],
						outputTypes: ["code", "file"]
					}
				],
				metadata: { priority: 4, type: "implementation" }
			},
			{
				id: "sub_task",
				name: "SubTask Agent",
				description: "Executes specific parts of larger tasks efficiently",
				version: "1.0.0",
				capabilities: [
					{
						name: "task_execution",
						description: "Execute specific sub-tasks efficiently",
						inputTypes: ["text", "file"],
						outputTypes: ["text", "file", "code"]
					}
				],
				metadata: { priority: 2, type: "execution" }
			}
		]

		// Register all agent cards
		agentCards.forEach(card => this._a2aProtocol.registerAgent(card))
	}



	/**
	 * Get A2A communication statistics
	 */
	public getA2AStats() {
		return this._a2aProtocol.getStats()
	}

	/**
	 * Discover agents by capability
	 */
	public discoverAgentsByCapability(capability: string): AgentCard[] {
		return this._a2aProtocol.discoverAgents(capability)
	}

	/**
	 * Get agent priority for task assignment
	 */
	private getAgentPriorityA2A(agentType: SpawnAgentOptions): number {
		const priorities = {
			"scholar": 15,    // Highest priority for fast learning operations
			"planner": 10,
			"analyzer": 8,
			"researcher": 6,
			"coder": 4,
			"sub_task": 2
		}
		return priorities[agentType] || 1
	}
}
