/**
 * @fileoverview API Request Queue Manager - Handles concurrent API requests from multiple sub-agents
 */

import PQueue from "p-queue"
import { Anthropic } from "@anthropic-ai/sdk"

export interface QueuedRequest {
	id: string
	agentId: string
	agentType: string
	priority: number
	request: () => Promise<any>
	timestamp: number
	retryCount: number
	maxRetries: number
}

export interface QueueStats {
	totalRequests: number
	completedRequests: number
	failedRequests: number
	averageWaitTime: number
	currentQueueSize: number
	activeRequests: number
}

export class ApiRequestQueue {
	private queue: PQueue
	private requestHistory: Map<string, QueuedRequest> = new Map()
	private stats: QueueStats = {
		totalRequests: 0,
		completedRequests: 0,
		failedRequests: 0,
		averageWaitTime: 0,
		currentQueueSize: 0,
		activeRequests: 0
	}
	private waitTimes: number[] = []

	constructor(options: {
		concurrency?: number
		intervalCap?: number
		interval?: number
		timeout?: number
	} = {}) {
		this.queue = new PQueue({
			concurrency: options.concurrency || 3, // Max 3 concurrent API requests
			intervalCap: options.intervalCap || 10, // Max 10 requests per interval
			interval: options.interval || 60000, // 1 minute interval
			timeout: options.timeout || 120000, // 2 minute timeout
		})

		// Monitor queue events
		this.queue.on('active', () => {
			this.stats.activeRequests = this.queue.pending
			this.stats.currentQueueSize = this.queue.size
		})

		this.queue.on('idle', () => {
			this.stats.activeRequests = 0
			this.stats.currentQueueSize = 0
		})
	}

	/**
	 * Add API request to queue with priority
	 */
	public async enqueueRequest<T>(
		agentId: string,
		agentType: string,
		request: () => Promise<T>,
		priority: number = 0,
		maxRetries: number = 3
	): Promise<T> {
		const requestId = `${agentId}_${Date.now()}_${Math.random()}`
		const queuedRequest: QueuedRequest = {
			id: requestId,
			agentId,
			agentType,
			priority,
			request,
			timestamp: Date.now(),
			retryCount: 0,
			maxRetries
		}

		this.requestHistory.set(requestId, queuedRequest)
		this.stats.totalRequests++

		const startTime = Date.now()

		try {
			const result = await this.queue.add(
				async () => {
					const waitTime = Date.now() - startTime
					this.waitTimes.push(waitTime)
					this.updateAverageWaitTime()

					return await this.executeWithRetry(queuedRequest)
				},
				{ priority }
			)

			this.stats.completedRequests++
			return result as T
		} catch (error) {
			this.stats.failedRequests++
			console.error(`API request failed for agent ${agentId}:`, error)
			throw error
		}
	}

	/**
	 * Execute request with retry logic
	 */
	private async executeWithRetry(queuedRequest: QueuedRequest): Promise<any> {
		while (queuedRequest.retryCount <= queuedRequest.maxRetries) {
			try {
				const result = await queuedRequest.request()
				return result
			} catch (error) {
				queuedRequest.retryCount++
				
				if (queuedRequest.retryCount > queuedRequest.maxRetries) {
					throw error
				}

				// Exponential backoff
				const delay = Math.min(1000 * Math.pow(2, queuedRequest.retryCount), 10000)
				await new Promise(resolve => setTimeout(resolve, delay))
				
				console.warn(`Retrying API request for agent ${queuedRequest.agentId} (attempt ${queuedRequest.retryCount}/${queuedRequest.maxRetries})`)
			}
		}
	}

	/**
	 * Get priority for agent type
	 */
	public getAgentPriority(agentType: string): number {
		const priorities = {
			"planner": 10,     // Highest priority
			"analyzer": 8,
			"researcher": 6,
			"coder": 4,
			"sub_task": 2      // Lowest priority
		}
		return priorities[agentType as keyof typeof priorities] || 1
	}

	/**
	 * Cancel requests for specific agent
	 */
	public cancelAgentRequests(agentId: string): void {
		// Remove pending requests for the agent
		this.queue.clear()
		
		// Mark agent requests as cancelled in history
		for (const [requestId, request] of this.requestHistory) {
			if (request.agentId === agentId) {
				this.requestHistory.delete(requestId)
			}
		}
	}

	/**
	 * Get queue statistics
	 */
	public getStats(): QueueStats {
		return {
			...this.stats,
			currentQueueSize: this.queue.size,
			activeRequests: this.queue.pending
		}
	}

	/**
	 * Get agent-specific statistics
	 */
	public getAgentStats(agentId: string): {
		totalRequests: number
		completedRequests: number
		failedRequests: number
		averageRetries: number
	} {
		const agentRequests = Array.from(this.requestHistory.values())
			.filter(req => req.agentId === agentId)

		const totalRequests = agentRequests.length
		const completedRequests = agentRequests.filter(req => req.retryCount <= req.maxRetries).length
		const failedRequests = totalRequests - completedRequests
		const averageRetries = totalRequests > 0 
			? agentRequests.reduce((sum, req) => sum + req.retryCount, 0) / totalRequests 
			: 0

		return {
			totalRequests,
			completedRequests,
			failedRequests,
			averageRetries
		}
	}

	/**
	 * Update average wait time
	 */
	private updateAverageWaitTime(): void {
		if (this.waitTimes.length === 0) {return}

		// Keep only last 100 wait times for rolling average
		if (this.waitTimes.length > 100) {
			this.waitTimes = this.waitTimes.slice(-100)
		}

		this.stats.averageWaitTime = this.waitTimes.reduce((sum, time) => sum + time, 0) / this.waitTimes.length
	}

	/**
	 * Check if queue is congested
	 */
	public isCongested(): boolean {
		return this.queue.size > 10 || this.stats.averageWaitTime > 30000 // 30 seconds
	}

	/**
	 * Get congestion level
	 */
	public getCongestionLevel(): "low" | "medium" | "high" | "critical" {
		const queueSize = this.queue.size
		const waitTime = this.stats.averageWaitTime

		if (queueSize > 20 || waitTime > 60000) {return "critical"}
		if (queueSize > 15 || waitTime > 45000) {return "high"}
		if (queueSize > 10 || waitTime > 30000) {return "medium"}
		return "low"
	}

	/**
	 * Pause queue processing
	 */
	public pause(): void {
		this.queue.pause()
	}

	/**
	 * Resume queue processing
	 */
	public resume(): void {
		this.queue.start()
	}

	/**
	 * Clear all pending requests
	 */
	public clear(): void {
		this.queue.clear()
		this.requestHistory.clear()
	}
}