/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Create a noun phrase extractor from a configuration.
 */

import { NounPhraseExtractorType } from '../../../../config/enums';
import { TextAnalyzerConfig } from '../../../../config/models/extract-graph-nlp-config';
import { BaseNounPhraseExtractor } from './base';
import { RegexENNounPhraseExtractor } from './regex-extractor';
import { EN_STOP_WORDS } from './stop-words';

/**
 * A factory class for creating noun phrase extractor.
 */
export class NounPhraseExtractorFactory {
    private static npExtractorTypes: Record<string, any> = {};

    /**
     * Register a noun phrase extractor type.
     */
    static register(npExtractorType: string, npExtractor: any): void {
        this.npExtractorTypes[npExtractorType] = npExtractor;
    }

    /**
     * Get the noun phrase extractor from configuration.
     */
    static getNpExtractor(config: TextAnalyzerConfig): BaseNounPhraseExtractor {
        const npExtractorType = config.extractorType;
        const excludeNouns = config.excludeNouns || EN_STOP_WORDS;

        switch (npExtractorType) {
            case NounPhraseExtractorType.Syntactic:
                // Simplified implementation - in reality you'd implement SyntacticNounPhraseExtractor
                console.warn('SyntacticNounPhraseExtractor not fully implemented, using RegexEN');
                return new RegexENNounPhraseExtractor(
                    excludeNouns,
                    config.maxWordLength,
                    config.wordDelimiter
                );
                
            case NounPhraseExtractorType.CFG:
                // Simplified implementation - in reality you'd implement CFGNounPhraseExtractor
                console.warn('CFGNounPhraseExtractor not fully implemented, using RegexEN');
                return new RegexENNounPhraseExtractor(
                    excludeNouns,
                    config.maxWordLength,
                    config.wordDelimiter
                );
                
            case NounPhraseExtractorType.RegexEnglish:
                return new RegexENNounPhraseExtractor(
                    excludeNouns,
                    config.maxWordLength,
                    config.wordDelimiter
                );
                
            default:
                throw new Error(`Unknown noun phrase extractor type: ${npExtractorType}`);
        }
    }
}

/**
 * Create a noun phrase extractor from a configuration.
 */
export function createNounPhraseExtractor(
    analyzerConfig: TextAnalyzerConfig
): BaseNounPhraseExtractor {
    return NounPhraseExtractorFactory.getNpExtractor(analyzerConfig);
}