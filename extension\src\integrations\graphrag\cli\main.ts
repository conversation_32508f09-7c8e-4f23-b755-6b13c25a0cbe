// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * CLI entrypoint.
 */

import * as path from 'path';
import { graphragConfigDefaults } from '../config/defaults';
import { IndexingMethod, SearchMethod } from '../config/enums';
import { DocSelectionType } from '../prompt_tune/types';

// Constants from prompt_tune defaults
const LIMIT = 15;
const MAX_TOKEN_COUNT = 8000;
const N_SUBSET_MAX = 300;
const K = 15;

const INVALID_METHOD_ERROR = "Invalid method";

/**
 * CLI interface for GraphRAG commands.
 */
export interface GraphRagCLI {
    init(options: InitOptions): Promise<void>;
    index(options: IndexOptions): Promise<void>;
    update(options: UpdateOptions): Promise<void>;
    promptTune(options: PromptTuneOptions): Promise<void>;
    query(options: QueryOptions): Promise<void>;
}

export interface InitOptions {
    root?: string;
    force?: boolean;
}

export interface IndexOptions {
    config?: string;
    root?: string;
    method?: IndexingMethod;
    verbose?: boolean;
    memprofile?: boolean;
    dryRun?: boolean;
    cache?: boolean;
    skipValidation?: boolean;
    output?: string;
}

export interface UpdateOptions {
    config?: string;
    root?: string;
    method?: IndexingMethod;
    verbose?: boolean;
    memprofile?: boolean;
    cache?: boolean;
    skipValidation?: boolean;
    output?: string;
}

export interface PromptTuneOptions {
    root?: string;
    config?: string;
    verbose?: boolean;
    domain?: string;
    selectionMethod?: DocSelectionType;
    nSubsetMax?: number;
    k?: number;
    limit?: number;
    maxTokens?: number;
    minExamplesRequired?: number;
    chunkSize?: number;
    overlap?: number;
    language?: string;
    discoverEntityTypes?: boolean;
    output?: string;
}

export interface QueryOptions {
    method: SearchMethod;
    query: string;
    config?: string;
    verbose?: boolean;
    data?: string;
    root?: string;
    communityLevel?: number;
    dynamicCommunitySelection?: boolean;
    responseType?: string;
    streaming?: boolean;
}

/**
 * Implementation of GraphRAG CLI.
 */
export class GraphRagCLIImpl implements GraphRagCLI {
    /**
     * Generate a default configuration file.
     */
    async init(options: InitOptions = {}): Promise<void> {
        const { root = process.cwd(), force = false } = options;
        const { initializeProjectAt } = await import('./initialize');
        await initializeProjectAt(root, force);
    }

    /**
     * Build a knowledge graph index.
     */
    async index(options: IndexOptions = {}): Promise<void> {
        const {
            config,
            root = process.cwd(),
            method = IndexingMethod.STANDARD,
            verbose = false,
            memprofile = false,
            dryRun = false,
            cache = true,
            skipValidation = false,
            output
        } = options;

        const { indexCli } = await import('./index');
        await indexCli({
            rootDir: root,
            verbose,
            memprofile,
            cache,
            configFilepath: config,
            dryRun,
            skipValidation,
            outputDir: output,
            method,
        });
    }

    /**
     * Update an existing knowledge graph index.
     */
    async update(options: UpdateOptions = {}): Promise<void> {
        const {
            config,
            root = process.cwd(),
            method = IndexingMethod.STANDARD,
            verbose = false,
            memprofile = false,
            cache = true,
            skipValidation = false,
            output
        } = options;

        const { updateCli } = await import('./index');
        await updateCli({
            rootDir: root,
            verbose,
            memprofile,
            cache,
            configFilepath: config,
            skipValidation,
            outputDir: output,
            method,
        });
    }

    /**
     * Generate custom graphrag prompts with your own data (i.e. auto templating).
     */
    async promptTune(options: PromptTuneOptions = {}): Promise<void> {
        const {
            root = process.cwd(),
            config,
            verbose = false,
            domain,
            selectionMethod = DocSelectionType.RANDOM,
            nSubsetMax = N_SUBSET_MAX,
            k = K,
            limit = LIMIT,
            maxTokens = MAX_TOKEN_COUNT,
            minExamplesRequired = 2,
            chunkSize = graphragConfigDefaults.chunks.size,
            overlap = graphragConfigDefaults.chunks.overlap,
            language,
            discoverEntityTypes = true,
            output = "prompts"
        } = options;

        const { promptTune } = await import('./prompt-tune');
        await promptTune({
            root,
            config,
            domain,
            verbose,
            selectionMethod,
            limit,
            maxTokens,
            chunkSize,
            overlap,
            language,
            discoverEntityTypes,
            output,
            nSubsetMax,
            k,
            minExamplesRequired,
        });
    }

    /**
     * Query a knowledge graph index.
     */
    async query(options: QueryOptions): Promise<void> {
        const {
            method,
            query,
            config,
            verbose = false,
            data,
            root = process.cwd(),
            communityLevel = 2,
            dynamicCommunitySelection = false,
            responseType = "Multiple Paragraphs",
            streaming = false
        } = options;

        const {
            runBasicSearch,
            runDriftSearch,
            runGlobalSearch,
            runLocalSearch,
        } = await import('./query');

        switch (method) {
            case SearchMethod.LOCAL:
                await runLocalSearch({
                    configFilepath: config,
                    dataDir: data,
                    rootDir: root,
                    communityLevel,
                    responseType,
                    streaming,
                    query,
                    verbose,
                });
                break;
            case SearchMethod.GLOBAL:
                await runGlobalSearch({
                    configFilepath: config,
                    dataDir: data,
                    rootDir: root,
                    communityLevel,
                    dynamicCommunitySelection,
                    responseType,
                    streaming,
                    query,
                    verbose,
                });
                break;
            case SearchMethod.DRIFT:
                await runDriftSearch({
                    configFilepath: config,
                    dataDir: data,
                    rootDir: root,
                    communityLevel,
                    streaming,
                    responseType,
                    query,
                    verbose,
                });
                break;
            case SearchMethod.BASIC:
                await runBasicSearch({
                    configFilepath: config,
                    dataDir: data,
                    rootDir: root,
                    streaming,
                    query,
                    verbose,
                });
                break;
            default:
                throw new Error(INVALID_METHOD_ERROR);
        }
    }
}

/**
 * Create a new GraphRAG CLI instance.
 */
export function createGraphRagCLI(): GraphRagCLI {
    return new GraphRagCLIImpl();
}