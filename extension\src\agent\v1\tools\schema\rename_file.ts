// schema/rename_file.ts
import { z } from "zod"

/**
 * @tool rename_file
 * @description SINGLE FILE RENAME TOOL - For renaming ONE file or directory
 * 
 * This tool is specifically designed for single file/directory rename operations.
 * Use this when you need to rename exactly ONE file or directory.
 * The file remains in the same directory, only the name is changed.
 * 
 * SINGLE FILE MODE: For renaming ONE file
 *    Parameters: path, new_name, dry_run (optional)
 *    Use case: Rename one specific file or directory
 *    Benefits: Simple, focused, clear intent
 * 
 * PREVIEW FUNCTIONALITY (dry_run):
 * - Set dry_run=true to preview rename without applying it
 * - Shows exactly what the file would be renamed to
 * - Safe way to verify the operation before execution
 * 
 * FEATURES:
 * - File remains in the same directory
 * - Clear error messages for permission issues
 * - Path validation and security checks
 * - Supports both files and directories
 * 
 * WHEN TO USE:
 * - Use this tool when you need to rename exactly ONE file or directory
 * - For changing a file's name while keeping it in the same location
 * - For renaming directories
 * - For fixing file naming conventions on individual files
 * 
 * DO NOT USE for multiple files - use batch_rename_files instead
 * @example (SINGLE FILE MODE - One file only)
 * ```xml
 * <tool name="rename_file">
 *   <path>src/component.tsx</path>
 *   <new_name>Button.tsx</new_name>
 * </tool>
 * ```
 * 
 * @example (BATCH MODE - Multiple files - RECOMMENDED for bulk operations)
 * ```xml
 * <tool name="rename_file">
 *   <files>
 *     <file>
 *       <path>src/Button.jsx</path>
 *       <new_name>Button.tsx</new_name>
 *     </file>
 *     <file>
 *       <path>src/Input.jsx</path>
 *       <new_name>Input.tsx</new_name>
 *     </file>
 *     <file>
 *       <path>src/Modal.jsx</path>
 *       <new_name>Modal.tsx</new_name>
 *     </file>
 *   </files>
 * </tool>
 * ```
 * 
 * @example (BATCH MODE - Convert all .jsx to .tsx - EFFICIENT)
 * ```xml
 * <tool name="rename_file">
 *   <files>
 *     <file><path>src/App.jsx</path><new_name>App.tsx</new_name></file>
 *     <file><path>src/Header.jsx</path><new_name>Header.tsx</new_name></file>
 *     <file><path>src/Footer.jsx</path><new_name>Footer.tsx</new_name></file>
 *     <file><path>src/Sidebar.jsx</path><new_name>Sidebar.tsx</new_name></file>
 *   </files>
 * </tool>
 * ```
 * 
 * @example (BATCH MODE - Standardize config files)
 * ```xml
 * <tool name="rename_file">
 *   <files>
 *     <file><path>config.json</path><new_name>app.config.json</new_name></file>
 *     <file><path>settings.json</path><new_name>user.settings.json</new_name></file>
 *     <file><path>database.json</path><new_name>db.config.json</new_name></file>
 *   </files>
 * </tool>
 * ```
 */
const schema = z.object({
	path: z.string().describe("The current path of the file or directory to rename (relative to the current working directory)."),
	new_name: z.string().describe("The new name for the file or directory (filename only, without directory path)."),
	dry_run: z.boolean().optional().describe("Preview rename without applying it. Defaults to false."),
})

const examples = [
	// Single file examples
	`<rename_file>
  <path>src/component.tsx</path>
  <new_name>Button.tsx</new_name>
</rename_file>`,

	`<rename_file>
  <path>docs/guide.md</path>
  <new_name>user-guide.md</new_name>
  <dry_run>true</dry_run>
</rename_file>`,

	`<rename_file>
  <path>src/utils/helper.js</path>
  <new_name>utils.js</new_name>
</rename_file>`,

	// Directory rename
	`<rename_file>
  <path>src/old_folder</path>
  <new_name>new_folder</new_name>
</rename_file>`,
]

export const renameFileTool = {
	schema: {
		name: "rename_file",
		schema,
	},
	examples,
}

export type RenameFileToolParams = {
	name: "rename_file"
	input: z.infer<typeof schema>
}