/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing _get_num_total, chunk, run_strategy and load_strategy methods definitions.
 */

import { DataFrame } from '../../../data-model/types';
import { WorkflowCallbacks } from '../../../callbacks/workflow-callbacks';
import { ChunkingConfig, ChunkStrategyType } from '../../../config/models/chunking-config';
import { ChunkInput, ChunkStrategy, TextChunk } from './typing';
import { runTokens, runSentences } from './strategies';
import { bootstrap } from './bootstrap';

/**
 * Chunk a piece of text into smaller pieces.
 * 
 * ## Usage
 * ```yaml
 * args:
 *     column: <column name> # The name of the column containing the text to chunk
 *     strategy: <strategy config> # The strategy to use to chunk the text
 * ```
 * 
 * ## Strategies
 * The text chunk verb uses a strategy to chunk the text.
 * 
 * ### tokens
 * This strategy uses tokens to chunk a piece of text.
 * 
 * ### sentence
 * This strategy chunks text into sentences.
 */
export function chunkText(
    input: DataFrame,
    column: string,
    size: number,
    overlap: number,
    encodingModel: string,
    strategy: ChunkStrategyType,
    callbacks: WorkflowCallbacks,
): any[] {
    const strategyExec = loadStrategy(strategy);
    const numTotal = getNumTotal(input, column);
    
    let completed = 0;
    const tick = (increment: number) => {
        completed += increment;
        if (callbacks.progress) {
            callbacks.progress(completed / numTotal, 'Chunking text');
        }
    };

    // Create config object
    const config: ChunkingConfig = {
        size: size,
        overlap: overlap,
        encodingModel: encodingModel
    };

    // Process each row
    return input.data.map(row => {
        return runStrategy(strategyExec, row[column], config, tick);
    });
}

/**
 * Run strategy method definition.
 */
export function runStrategy(
    strategyExec: ChunkStrategy,
    input: ChunkInput,
    config: ChunkingConfig,
    tick: (increment: number) => void,
): Array<string | [string[] | null, string, number]> {
    if (typeof input === 'string') {
        const chunks = Array.from(strategyExec([input], config, tick));
        return chunks.map(item => item.textChunk);
    }

    // Handle array inputs
    const texts = Array.isArray(input) 
        ? input.map(item => typeof item === 'string' ? item : item[1])
        : [];

    const strategyResults = Array.from(strategyExec(texts, config, tick));

    const results: Array<string | [string[] | null, string, number]> = [];
    
    for (const strategyResult of strategyResults) {
        const docIndices = strategyResult.sourceDocIndices;
        
        if (Array.isArray(input) && typeof input[docIndices[0]] === 'string') {
            results.push(strategyResult.textChunk);
        } else if (Array.isArray(input)) {
            const docIds = docIndices.map(docIdx => {
                const item = input[docIdx];
                return Array.isArray(item) ? item[0] : null;
            });
            results.push([
                docIds,
                strategyResult.textChunk,
                strategyResult.nTokens || 0,
            ]);
        }
    }
    
    return results;
}

/**
 * Load strategy method definition.
 */
export function loadStrategy(strategy: ChunkStrategyType): ChunkStrategy {
    switch (strategy) {
        case ChunkStrategyType.tokens:
            return runTokens;
        case ChunkStrategyType.sentence:
            bootstrap();
            return runSentences;
        default:
            throw new Error(`Unknown strategy: ${strategy}`);
    }
}

/**
 * Get total number of items to process.
 */
function getNumTotal(output: DataFrame, column: string): number {
    let numTotal = 0;
    
    for (const row of output.data) {
        const value = row[column];
        if (typeof value === 'string') {
            numTotal += 1;
        } else if (Array.isArray(value)) {
            numTotal += value.length;
        }
    }
    
    return numTotal;
}