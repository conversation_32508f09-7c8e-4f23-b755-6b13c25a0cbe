// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * CLI implementation of the index subcommand.
 */

import * as path from 'path';
import * as process from 'process';
import * as api from '../api';
import { CacheType, IndexingMethod, ReportingType } from '../config/enums';
import { loadConfig } from '../config/load_config';
import { validateConfigNames } from '../index/validate_config';
import { redact } from '../utils/cli';
import { initLoggers } from '../logger/standard_logging';

// Create logger
const logger = console; // Simplified logging for TypeScript

/**
 * Register signal handlers for graceful shutdown
 */
function registerSignalHandlers(): void {
    const handleSignal = (signal: string) => {
        logger.debug(`Received signal ${signal}, exiting...`);
        // In Node.js, we don't have asyncio.all_tasks(), but we can handle cleanup differently
        logger.debug("Handling graceful shutdown...");
        process.exit(0);
    };

    // Register signal handlers for SIGINT and SIGHUP
    process.on('SIGINT', () => handleSignal('SIGINT'));
    
    if (process.platform !== 'win32') {
        process.on('SIGHUP', () => handleSignal('SIGHUP'));
    }
}

/**
 * CLI interface for index command
 */
export interface IndexCliOptions {
    rootDir: string;
    method: IndexingMethod;
    verbose: boolean;
    memprofile: boolean;
    cache: boolean;
    configFilepath?: string;
    dryRun: boolean;
    skipValidation: boolean;
    outputDir?: string;
}

/**
 * Run the indexing pipeline with the given config
 */
export async function indexCli(options: IndexCliOptions): Promise<void> {
    const cliOverrides: Record<string, any> = {};
    
    if (options.outputDir) {
        cliOverrides["output.base_dir"] = options.outputDir;
        cliOverrides["reporting.base_dir"] = options.outputDir;
        cliOverrides["update_index_output.base_dir"] = options.outputDir;
    }
    
    const config = await loadConfig(options.rootDir, options.configFilepath, cliOverrides);
    
    await runIndex({
        config,
        method: options.method,
        isUpdateRun: false,
        verbose: options.verbose,
        memprofile: options.memprofile,
        cache: options.cache,
        dryRun: options.dryRun,
        skipValidation: options.skipValidation,
    });
}

/**
 * CLI interface for update command
 */
export interface UpdateCliOptions {
    rootDir: string;
    method: IndexingMethod;
    verbose: boolean;
    memprofile: boolean;
    cache: boolean;
    configFilepath?: string;
    skipValidation: boolean;
    outputDir?: string;
}

/**
 * Run the update pipeline with the given config
 */
export async function updateCli(options: UpdateCliOptions): Promise<void> {
    const cliOverrides: Record<string, any> = {};
    
    if (options.outputDir) {
        cliOverrides["output.base_dir"] = options.outputDir;
        cliOverrides["reporting.base_dir"] = options.outputDir;
        cliOverrides["update_index_output.base_dir"] = options.outputDir;
    }

    const config = await loadConfig(options.rootDir, options.configFilepath, cliOverrides);

    await runIndex({
        config,
        method: options.method,
        isUpdateRun: true,
        verbose: options.verbose,
        memprofile: options.memprofile,
        cache: options.cache,
        dryRun: false,
        skipValidation: options.skipValidation,
    });
}

/**
 * Internal interface for run index options
 */
interface RunIndexOptions {
    config: any;
    method: IndexingMethod;
    isUpdateRun: boolean;
    verbose: boolean;
    memprofile: boolean;
    cache: boolean;
    dryRun: boolean;
    skipValidation: boolean;
}

/**
 * Internal function to run the indexing pipeline
 */
async function runIndex(options: RunIndexOptions): Promise<void> {
    const {
        config,
        method,
        isUpdateRun,
        verbose,
        memprofile,
        cache,
        dryRun,
        skipValidation,
    } = options;

    // Initialize loggers and reporting config
    await initLoggers({
        config,
        rootDir: config.root_dir || undefined,
        verbose,
    });

    if (!cache) {
        config.cache.type = CacheType.NONE;
    }

    // Log the configuration details
    if (config.reporting.type === ReportingType.FILE) {
        const logDir = path.join(config.root_dir || "", config.reporting.base_dir || "");
        const logPath = path.join(logDir, "logs.txt");
        logger.info(`Logging enabled at ${logPath}`);
    } else {
        logger.info(`Logging not enabled for config ${redact(config)}`);
    }

    if (!skipValidation) {
        validateConfigNames(config);
    }

    logger.info(`Starting pipeline run. Dry run: ${dryRun}`);
    logger.info(`Using configuration: ${redact(config)}`);

    if (dryRun) {
        logger.info("Dry run complete, exiting...");
        process.exit(0);
    }

    registerSignalHandlers();

    try {
        const outputs = await api.buildIndex({
            config,
            method,
            isUpdateRun,
            memoryProfile: memprofile,
        });

        const encounteredErrors = outputs.some(
            output => output.errors && output.errors.length > 0
        );

        if (encounteredErrors) {
            logger.error("Errors occurred during the pipeline run, see logs for more details.");
            process.exit(1);
        } else {
            logger.info("All workflows completed successfully.");
            process.exit(0);
        }
    } catch (error) {
        logger.error("Pipeline execution failed:", error);
        process.exit(1);
    }
}
