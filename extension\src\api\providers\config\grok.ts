// providers/grok.ts
import { ProviderConfig } from "../types"
import { DEFAULT_BASE_URLS, PROVIDER_IDS, PROVIDER_NAMES } from "../constants"

export const grokConfig: ProviderConfig = {
    id: PROVIDER_IDS.GROK,
    name: PROVIDER_NAMES[PROVIDER_IDS.GROK],
    baseUrl: DEFAULT_BASE_URLS[PROVIDER_IDS.GROK],
    models: [
        {
            id: "grok-4",
            name: "Grok 4",
            contextWindow: 131072, // 131,072 tokens (128K + overhead)
            maxTokens: 4096, // Conservative max tokens for stability
            supportsImages: false, // Vision support coming in future updates
            supportsPromptCache: true, // Supports token caching
            inputPrice: 2.5, // $2.50 per 1M tokens
            outputPrice: 12.0, // $12.00 per 1M tokens
            cacheReadsPrice: 1.25, // 50% of input price for cache reads
            cacheWritesPrice: 2.5, // Same as input price for cache writes
            provider: PROVIDER_IDS.GROK,
            isRecommended: true,
        },
        {
            id: "grok-4-heavy",
            name: "Grok 4 Heavy",
            contextWindow: 131072, // 131,072 tokens (128K + overhead)
            maxTokens: 8192, // Higher max tokens for complex tasks
            supportsImages: false, // Vision support coming in future updates
            supportsPromptCache: true, // Supports token caching
            inputPrice: 5.0, // Estimated higher price for heavy model
            outputPrice: 20.0, // Estimated higher price for heavy model
            cacheReadsPrice: 2.5, // 50% of input price for cache reads
            cacheWritesPrice: 5.0, // Same as input price for cache writes
            provider: PROVIDER_IDS.GROK,
        },
    ],
    requiredFields: ["apiKey"],
}