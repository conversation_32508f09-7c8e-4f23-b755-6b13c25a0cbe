/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing create_input method definition.
 */

import { DataFrame } from '../../data-model/types';
import { InputFileType } from '../../config/enums';
import { InputConfig } from '../../config/models/input-config';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { loadCsv } from './csv';
import { loadJson } from './json';
import { loadText } from './text';

const logger = console;

// Type for loader functions
type LoaderFunction = (config: InputConfig, storage: PipelineStorage) => Promise<DataFrame>;

// Loaders mapping
const loaders: Record<string, LoaderFunction> = {
    [InputFileType.text]: loadText,
    [InputFileType.csv]: loadCsv,
    [InputFileType.json]: loadJson,
};

/**
 * Instantiate input data for a pipeline.
 * @param config - Input configuration
 * @param storage - Pipeline storage instance
 * @returns Promise resolving to DataFrame with input data
 */
export async function createInput(
    config: InputConfig,
    storage: PipelineStorage,
): Promise<DataFrame> {
    logger.info(`loading input from root_dir=${config.storage.baseDir}`);

    if (config.fileType in loaders) {
        logger.info(`Loading Input ${config.fileType}`);
        const loader = loaders[config.fileType];
        const result = await loader(config, storage);
        
        // Convert metadata columns to strings and collapse them into a JSON object
        if (config.metadata) {
            const metadataColumns = config.metadata;
            const hasAllColumns = metadataColumns.every(col => col in result.columns);
            
            if (hasAllColumns) {
                // Collapse the metadata columns into a single JSON object column
                result.data = result.data.map(row => ({
                    ...row,
                    metadata: metadataColumns.reduce((meta, col) => {
                        meta[col] = row[col];
                        return meta;
                    }, {} as Record<string, any>)
                }));
            } else {
                throw new Error("One or more metadata columns not found in the DataFrame.");
            }

            // Convert metadata columns to strings
            result.data = result.data.map(row => {
                const updatedRow = { ...row };
                metadataColumns.forEach(col => {
                    if (col in updatedRow) {
                        updatedRow[col] = String(updatedRow[col]);
                    }
                });
                return updatedRow;
            });
        }

        return result;
    }

    throw new Error(`Unknown input type ${config.fileType}`);
}