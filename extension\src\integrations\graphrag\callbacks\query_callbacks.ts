// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Query Callbacks.
 */

import { BaseLLMCallback } from './llm-callbacks';

// Forward declaration for SearchResult - would be imported from actual module
export interface SearchResult {
    [key: string]: any;
}

/**
 * Callbacks used during query execution.
 */
export interface QueryCallbacks extends BaseLLMCallback {
    /**
     * Handle when context data is constructed.
     */
    onContext(context: any): void;

    /**
     * Handle the start of map operation.
     */
    onMapResponseStart(mapResponseContexts: string[]): void;

    /**
     * Handle the end of map operation.
     */
    onMapResponseEnd(mapResponseOutputs: SearchResult[]): void;

    /**
     * Handle the start of reduce operation.
     */
    onReduceResponseStart(reduceResponseContext: string | Record<string, any>): void;

    /**
     * Handle the end of reduce operation.
     */
    onReduceResponseEnd(reduceResponseOutput: string): void;

    /**
     * Handle when a new token is generated.
     */
    onLlmNewToken(token: string): void;
}