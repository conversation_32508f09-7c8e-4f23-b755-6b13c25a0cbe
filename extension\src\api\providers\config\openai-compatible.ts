import { ProviderConfig, ModelInfo } from "../types"
import { PROVIDER_IDS } from "../constants"

// Interface for Ollama model
export interface OllamaModel {
  id: string;
  name: string;
}

/**
 * Fetches models from Ollama API
 * @param baseUrl The base URL of the Ollama API (e.g., http://localhost:11434/v1)
 * @returns Promise that resolves to an array of OllamaModel objects
 */
export async function fetchOllamaModels(baseUrl: string): Promise<OllamaModel[]> {
  try {
    const baseApiUrl = baseUrl.replace(/\/v1$/, '');
    const response = await fetch(`${baseApiUrl}/api/tags`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return Array.isArray(data?.models)
      ? data.models.map((model: any) => ({ id: model.name, name: model.name }))
      : [];
  } catch (error) {
    console.warn('Failed to fetch Ollama models:', error);
    return [];
  }
}


// 动态 getter 映射到全局 providerSettingsAtom
let _openaiCompatibleSettings: any = {
  baseUrl: "http://localhost:11434/v1",
  inputLimit: 4096,
  outputLimit: 2048,
  supportImages: false,
  inputTokensPrice: 0,
  outputTokensPrice: 0,
};

export function setOpenAICompatibleSettings(settings: any) {
  _openaiCompatibleSettings = { ..._openaiCompatibleSettings, ...settings };
}

/**
 * Creates a ModelInfo object from an Ollama model
 */
function createModelFromOllama(ollamaModel: OllamaModel): ModelInfo {
  return {
    id: ollamaModel.id,
    name: ollamaModel.name,
    get contextWindow() {
      return Number(_openaiCompatibleSettings.inputLimit) || 4096;
    },
    get maxTokens() {
      return Number(_openaiCompatibleSettings.outputLimit) || 2048;
    },
    get supportsImages() {
      return !!_openaiCompatibleSettings.supportImages;
    },
    get inputPrice() {
      return Number(_openaiCompatibleSettings.inputTokensPrice) || 0;
    },
    get outputPrice() {
      return Number(_openaiCompatibleSettings.outputTokensPrice) || 0;
    },
    provider: PROVIDER_IDS.OPENAICOMPATIBLE,
  };
}

/**
 * Creates a placeholder model when no models are available
 */
function createPlaceholderModel(): ModelInfo {
  return {
    id: "no-models-available",
    name: "请拉取模型 - No models available",
    get contextWindow() {
      return Number(_openaiCompatibleSettings.inputLimit) || 4096;
    },
    get maxTokens() {
      return Number(_openaiCompatibleSettings.outputLimit) || 2048;
    },
    get supportsImages() {
      return !!_openaiCompatibleSettings.supportImages;
    },
    get inputPrice() {
      return 0;
    },
    get outputPrice() {
      return 0;
    },
    provider: PROVIDER_IDS.OPENAICOMPATIBLE,
  };
}

/**
 * Dynamic model fetcher for OpenAI Compatible providers (Ollama, LM Studio, etc.)
 */
async function getOpenAICompatibleModels(): Promise<ModelInfo[]> {
  const baseUrl = _openaiCompatibleSettings.baseUrl || "http://localhost:11434/v1";
  const ollamaModels = await fetchOllamaModels(baseUrl);

  // If no models are available, return a placeholder to guide the user
  if (ollamaModels.length === 0) {
    return [createPlaceholderModel()];
  }

  // Return the actual models from the API
  return ollamaModels.map(createModelFromOllama);
}

export const openaiCompatibleConfig: ProviderConfig = {
  id: PROVIDER_IDS.OPENAICOMPATIBLE,
  name: "OpenAI Compatible", // Supports Ollama, LM Studio, and other OpenAI-compatible APIs
  baseUrl: "http://localhost:11434/v1",
  models: [], // Empty array - models are fetched dynamically
  getModels: getOpenAICompatibleModels, // Dynamic model fetcher
  requiredFields: ["baseUrl", "modelId"],
}
