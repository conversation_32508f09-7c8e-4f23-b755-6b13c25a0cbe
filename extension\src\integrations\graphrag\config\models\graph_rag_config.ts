// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the default configuration.
 */

import * as path from 'path';
import * as fs from 'fs';
import * as defs from '../defaults';
import { LanguageModelConfigMissingError } from '../errors';
import { 
    InputFileType, 
    StorageType, 
    ReportingType,
    CacheType,
    ChunkStrategyType,
    ModelType,
    AuthType,
    AsyncType,
    NounPhraseExtractorType
} from '../enums';

// Import all the configuration interfaces

/**
 * Language model configuration interface.
 */
export interface LanguageModelConfig {
    api_key?: string;
    auth_type: AuthType;
    type: ModelType | string;
    model: string;
    encoding_model: string;
    api_base?: string;
    api_version?: string;
    deployment_name?: string;
    organization?: string;
    proxy?: string;
    audience?: string;
    model_supports_json?: boolean;
    request_timeout: number;
    tokens_per_minute: number | "auto" | null;
    requests_per_minute: number | "auto" | null;
    retry_strategy: string;
    max_retries: number;
    max_retry_wait: number;
    concurrent_requests: number;
    async_mode: AsyncType;
    responses?: (string | any)[] | null;
    max_tokens?: number | null;
    temperature: number;
    max_completion_tokens?: number | null;
    reasoning_effort?: string | null;
    top_p: number;
    n: number;
    frequency_penalty: number;
    presence_penalty: number;
}

/**
 * Storage configuration interface.
 */
export interface StorageConfig {
    type: StorageType;
    base_dir: string;
    connection_string?: string | null;
    container_name?: string | null;
    storage_account_blob_url?: string | null;
    cosmosdb_account_url?: string | null;
}

/**
 * Input configuration interface.
 */
export interface InputConfig {
    storage: StorageConfig;
    file_type: InputFileType;
    encoding: string;
    file_pattern: string;
    file_filter?: Record<string, string> | null;
    text_column: string;
    title_column?: string | null;
    metadata?: string[] | null;
}

/**
 * Chunking configuration interface.
 */
export interface ChunkingConfig {
    size: number;
    overlap: number;
    group_by_columns: string[];
    strategy: ChunkStrategyType;
    encoding_model: string;
    prepend_metadata: boolean;
    chunk_size_includes_metadata: boolean;
}

/**
 * Cache configuration interface.
 */
export interface CacheConfig {
    type: CacheType;
    base_dir: string;
    connection_string?: string | null;
    container_name?: string | null;
    storage_account_blob_url?: string | null;
    cosmosdb_account_url?: string | null;
}

/**
 * Reporting configuration interface.
 */
export interface ReportingConfig {
    type: ReportingType;
    base_dir: string;
    connection_string?: string | null;
    container_name?: string | null;
    storage_account_blob_url?: string | null;
}

/**
 * Vector store configuration interface.
 */
export interface VectorStoreConfig {
    type: string;
    db_uri?: string | null;
    url?: string | null;
    api_key?: string | null;
    audience?: string | null;
    container_name: string;
    database_name?: string | null;
    overwrite: boolean;
}

/**
 * Text embedding configuration interface.
 */
export interface TextEmbeddingConfig {
    model_id: string;
    vector_store_id: string;
    batch_size: number;
    batch_max_tokens: number;
    names: string[];
    strategy?: Record<string, any> | null;
}

/**
 * Basic search configuration interface.
 */
export interface BasicSearchConfig {
    prompt?: string | null;
    chat_model_id: string;
    embedding_model_id: string;
    k: number;
    max_context_tokens: number;
}

/**
 * Cluster graph configuration interface.
 */
export interface ClusterGraphConfig {
    max_cluster_size: number;
    use_lcc: boolean;
    seed: number;
}

/**
 * Community reports configuration interface.
 */
export interface CommunityReportsConfig {
    model_id: string;
    graph_prompt?: string | null;
    text_prompt?: string | null;
    max_length: number;
    max_input_length: number;
    strategy?: Record<string, any> | null;
}

/**
 * DRIFT search configuration interface.
 */
export interface DRIFTSearchConfig {
    prompt?: string | null;
    reduce_prompt?: string | null;
    chat_model_id: string;
    embedding_model_id: string;
    data_max_tokens: number;
    reduce_max_tokens?: number | null;
    reduce_temperature: number;
    reduce_max_completion_tokens?: number | null;
    concurrency: number;
    drift_k_followups: number;
    primer_folds: number;
    primer_llm_max_tokens: number;
    n_depth: number;
    local_search_text_unit_prop: number;
    local_search_community_prop: number;
    local_search_top_k_mapped_entities: number;
    local_search_top_k_relationships: number;
    local_search_max_data_tokens: number;
    local_search_temperature: number;
    local_search_top_p: number;
    local_search_n: number;
    local_search_llm_max_gen_tokens?: number | null;
    local_search_llm_max_gen_completion_tokens?: number | null;
}

/**
 * Embed graph configuration interface.
 */
export interface EmbedGraphConfig {
    enabled: boolean;
    dimensions: number;
    num_walks: number;
    walk_length: number;
    window_size: number;
    iterations: number;
    random_seed: number;
    use_lcc: boolean;
}

/**
 * Claim extraction configuration interface.
 */
export interface ClaimExtractionConfig {
    enabled: boolean;
    model_id: string;
    prompt?: string | null;
    description: string;
    max_gleanings: number;
    strategy?: Record<string, any> | null;
}

/**
 * Extract graph configuration interface.
 */
export interface ExtractGraphConfig {
    model_id: string;
    prompt?: string | null;
    entity_types: string[];
    max_gleanings: number;
    strategy?: Record<string, any> | null;
}

/**
 * Text analyzer configuration interface.
 */
export interface TextAnalyzerConfig {
    extractor_type: NounPhraseExtractorType;
    model_name: string;
    max_word_length: number;
    word_delimiter: string;
    include_named_entities: boolean;
    exclude_nouns?: string[] | null;
    exclude_entity_tags: string[];
    exclude_pos_tags: string[];
    noun_phrase_tags: string[];
    noun_phrase_grammars: Record<string, string>;
}

/**
 * Extract graph NLP configuration interface.
 */
export interface ExtractGraphNLPConfig {
    normalize_edge_weights: boolean;
    text_analyzer: TextAnalyzerConfig;
    concurrent_requests: number;
}

/**
 * Global search configuration interface.
 */
export interface GlobalSearchConfig {
    map_prompt?: string | null;
    reduce_prompt?: string | null;
    chat_model_id: string;
    knowledge_prompt?: string | null;
    max_context_tokens: number;
    data_max_tokens: number;
    map_max_length: number;
    reduce_max_length: number;
    dynamic_search_threshold: number;
    dynamic_search_keep_parent: boolean;
    dynamic_search_num_repeats: number;
    dynamic_search_use_summary: boolean;
    dynamic_search_max_level: number;
}

/**
 * Local search configuration interface.
 */
export interface LocalSearchConfig {
    prompt?: string | null;
    chat_model_id: string;
    embedding_model_id: string;
    text_unit_prop: number;
    community_prop: number;
    conversation_history_max_turns: number;
    top_k_entities: number;
    top_k_relationships: number;
    max_context_tokens: number;
}

/**
 * Prune graph configuration interface.
 */
export interface PruneGraphConfig {
    min_node_freq: number;
    max_node_freq_std?: number | null;
    min_node_degree: number;
    max_node_degree_std?: number | null;
    min_edge_weight_pct: number;
    remove_ego_nodes: boolean;
    lcc_only: boolean;
}

/**
 * Snapshots configuration interface.
 */
export interface SnapshotsConfig {
    embeddings: boolean;
    graphml: boolean;
    raw_graph: boolean;
}

/**
 * Summarize descriptions configuration interface.
 */
export interface SummarizeDescriptionsConfig {
    model_id: string;
    prompt?: string | null;
    max_length: number;
    max_input_tokens: number;
    strategy?: Record<string, any> | null;
}

/**
 * UMAP configuration interface.
 */
export interface UmapConfig {
    enabled: boolean;
}

/**
 * Base class for the Default-Configuration parameterization settings.
 */
export class GraphRagConfig {
    public root_dir: string;
    public models: Record<string, LanguageModelConfig>;
    public input: InputConfig;
    public chunks: ChunkingConfig;
    public output: StorageConfig;
    public outputs?: Record<string, StorageConfig> | null;
    public update_index_output: StorageConfig;
    public cache: CacheConfig;
    public reporting: ReportingConfig;
    public vector_store: Record<string, VectorStoreConfig>;
    public workflows?: string[] | null;
    public embed_text: TextEmbeddingConfig;
    public extract_graph: ExtractGraphConfig;
    public summarize_descriptions: SummarizeDescriptionsConfig;
    public extract_graph_nlp: ExtractGraphNLPConfig;
    public prune_graph: PruneGraphConfig;
    public cluster_graph: ClusterGraphConfig;
    public extract_claims: ClaimExtractionConfig;
    public community_reports: CommunityReportsConfig;
    public embed_graph: EmbedGraphConfig;
    public umap: UmapConfig;
    public snapshots: SnapshotsConfig;
    public local_search: LocalSearchConfig;
    public global_search: GlobalSearchConfig;
    public drift_search: DRIFTSearchConfig;
    public basic_search: BasicSearchConfig;

    constructor(values: Record<string, any> = {}) {
        // Initialize with defaults from graphrag_config_defaults
        this.root_dir = values.root_dir || defs.graphragConfigDefaults.root_dir || process.cwd();
        this.models = values.models || defs.graphragConfigDefaults.models || {};
        
        // Initialize input config
        this.input = values.input || {
            storage: {
                type: StorageType.FILE,
                base_dir: defs.inputDefaults.storage.base_dir,
                connection_string: null,
                container_name: null,
                storage_account_blob_url: null,
                cosmosdb_account_url: null
            },
            file_type: defs.inputDefaults.file_type,
            encoding: defs.inputDefaults.encoding,
            file_pattern: defs.inputDefaults.file_pattern,
            file_filter: defs.inputDefaults.file_filter,
            text_column: defs.inputDefaults.text_column,
            title_column: defs.inputDefaults.title_column,
            metadata: defs.inputDefaults.metadata
        };

        // Initialize chunks config
        this.chunks = values.chunks || {
            size: defs.chunksDefaults.size,
            overlap: defs.chunksDefaults.overlap,
            group_by_columns: defs.chunksDefaults.group_by_columns,
            strategy: defs.chunksDefaults.strategy,
            encoding_model: defs.chunksDefaults.encoding_model,
            prepend_metadata: defs.chunksDefaults.prepend_metadata,
            chunk_size_includes_metadata: defs.chunksDefaults.chunk_size_includes_metadata
        };

        // Initialize output config
        this.output = values.output || {
            type: StorageType.FILE,
            base_dir: defs.storageDefaults.base_dir,
            connection_string: null,
            container_name: null,
            storage_account_blob_url: null,
            cosmosdb_account_url: null
        };

        this.outputs = values.outputs || null;

        // Initialize update_index_output config
        this.update_index_output = values.update_index_output || {
            type: StorageType.FILE,
            base_dir: "update_output",
            connection_string: null,
            container_name: null,
            storage_account_blob_url: null,
            cosmosdb_account_url: null
        };

        // Initialize cache config
        this.cache = values.cache || {
            type: defs.cacheDefaults.type,
            base_dir: defs.cacheDefaults.base_dir,
            connection_string: defs.cacheDefaults.connection_string,
            container_name: defs.cacheDefaults.container_name,
            storage_account_blob_url: defs.cacheDefaults.storage_account_blob_url,
            cosmosdb_account_url: defs.cacheDefaults.cosmosdb_account_url
        };

        // Initialize reporting config
        this.reporting = values.reporting || {
            type: ReportingType.FILE,
            base_dir: "reports",
            connection_string: null,
            container_name: null,
            storage_account_blob_url: null
        };

        // Initialize vector_store config
        this.vector_store = values.vector_store || {};

        this.workflows = values.workflows || null;

        // Initialize embed_text config
        this.embed_text = values.embed_text || {
            model_id: defs.embedTextDefaults.model_id,
            vector_store_id: defs.embedTextDefaults.vector_store_id,
            batch_size: defs.embedTextDefaults.batch_size,
            batch_max_tokens: defs.embedTextDefaults.batch_max_tokens,
            names: defs.embedTextDefaults.names,
            strategy: defs.embedTextDefaults.strategy
        };

        // Initialize extract_graph config
        this.extract_graph = values.extract_graph || {
            model_id: defs.DEFAULT_CHAT_MODEL_ID,
            prompt: null,
            entity_types: [],
            max_gleanings: 1,
            strategy: null
        };

        // Initialize summarize_descriptions config
        this.summarize_descriptions = values.summarize_descriptions || {
            model_id: defs.DEFAULT_CHAT_MODEL_ID,
            prompt: null,
            max_length: 500,
            max_input_tokens: 8000,
            strategy: null
        };

        // Initialize extract_graph_nlp config
        this.extract_graph_nlp = values.extract_graph_nlp || {
            normalize_edge_weights: true,
            text_analyzer: {
                extractor_type: NounPhraseExtractorType.REGEX_ENGLISH,
                model_name: "en_core_web_sm",
                max_word_length: 50,
                word_delimiter: " ",
                include_named_entities: true,
                exclude_nouns: null,
                exclude_entity_tags: [],
                exclude_pos_tags: [],
                noun_phrase_tags: [],
                noun_phrase_grammars: {}
            },
            concurrent_requests: 4
        };

        // Initialize prune_graph config
        this.prune_graph = values.prune_graph || {
            min_node_freq: 1,
            max_node_freq_std: null,
            min_node_degree: 1,
            max_node_degree_std: null,
            min_edge_weight_pct: 0.0,
            remove_ego_nodes: false,
            lcc_only: false
        };

        // Initialize cluster_graph config
        this.cluster_graph = values.cluster_graph || {
            max_cluster_size: defs.clusterGraphDefaults.max_cluster_size,
            use_lcc: defs.clusterGraphDefaults.use_lcc,
            seed: defs.clusterGraphDefaults.seed
        };

        // Initialize extract_claims config
        this.extract_claims = values.extract_claims || {
            enabled: false,
            model_id: defs.DEFAULT_CHAT_MODEL_ID,
            prompt: null,
            description: "",
            max_gleanings: 1,
            strategy: null
        };

        // Initialize community_reports config
        this.community_reports = values.community_reports || {
            model_id: defs.communityReportDefaults.model_id,
            graph_prompt: defs.communityReportDefaults.graph_prompt,
            text_prompt: defs.communityReportDefaults.text_prompt,
            max_length: defs.communityReportDefaults.max_length,
            max_input_length: defs.communityReportDefaults.max_input_length,
            strategy: defs.communityReportDefaults.strategy
        };

        // Initialize embed_graph config
        this.embed_graph = values.embed_graph || {
            enabled: defs.embedGraphDefaults.enabled,
            dimensions: defs.embedGraphDefaults.dimensions,
            num_walks: defs.embedGraphDefaults.num_walks,
            walk_length: defs.embedGraphDefaults.walk_length,
            window_size: defs.embedGraphDefaults.window_size,
            iterations: defs.embedGraphDefaults.iterations,
            random_seed: defs.embedGraphDefaults.random_seed,
            use_lcc: defs.embedGraphDefaults.use_lcc
        };

        // Initialize umap config
        this.umap = values.umap || {
            enabled: false
        };

        // Initialize snapshots config
        this.snapshots = values.snapshots || {
            embeddings: false,
            graphml: false,
            raw_graph: false
        };

        // Initialize local_search config
        this.local_search = values.local_search || {
            prompt: null,
            chat_model_id: defs.DEFAULT_CHAT_MODEL_ID,
            embedding_model_id: defs.DEFAULT_EMBEDDING_MODEL_ID,
            text_unit_prop: 0.5,
            community_prop: 0.1,
            conversation_history_max_turns: 5,
            top_k_entities: 10,
            top_k_relationships: 10,
            max_context_tokens: 12000
        };

        // Initialize global_search config
        this.global_search = values.global_search || {
            map_prompt: null,
            reduce_prompt: null,
            chat_model_id: defs.DEFAULT_CHAT_MODEL_ID,
            knowledge_prompt: null,
            max_context_tokens: 12000,
            data_max_tokens: 12000,
            map_max_length: 1000,
            reduce_max_length: 2000,
            dynamic_search_threshold: 7,
            dynamic_search_keep_parent: false,
            dynamic_search_num_repeats: 1,
            dynamic_search_use_summary: true,
            dynamic_search_max_level: 3
        };

        // Initialize drift_search config
        this.drift_search = values.drift_search || {
            prompt: null,
            reduce_prompt: null,
            chat_model_id: defs.DEFAULT_CHAT_MODEL_ID,
            embedding_model_id: defs.DEFAULT_EMBEDDING_MODEL_ID,
            data_max_tokens: 12000,
            reduce_max_tokens: null,
            reduce_temperature: 0.0,
            reduce_max_completion_tokens: null,
            concurrency: 32,
            drift_k_followups: 3,
            primer_folds: 8,
            primer_llm_max_tokens: 8000,
            n_depth: 3,
            local_search_text_unit_prop: 0.5,
            local_search_community_prop: 0.1,
            local_search_top_k_mapped_entities: 10,
            local_search_top_k_relationships: 10,
            local_search_max_data_tokens: 12000,
            local_search_temperature: 0.0,
            local_search_top_p: 1.0,
            local_search_n: 1,
            local_search_llm_max_gen_tokens: null,
            local_search_llm_max_gen_completion_tokens: null
        };

        // Initialize basic_search config
        this.basic_search = values.basic_search || {
            prompt: defs.basicSearchDefaults.prompt,
            chat_model_id: defs.basicSearchDefaults.chat_model_id,
            embedding_model_id: defs.basicSearchDefaults.embedding_model_id,
            k: defs.basicSearchDefaults.k,
            max_context_tokens: defs.basicSearchDefaults.max_context_tokens
        };

        // Validate the configuration
        this._validateModel();
    }

    /**
     * Get a string representation.
     */
    public toString(): string {
        return JSON.stringify(this, null, 4);
    }

    /**
     * Validate the root directory.
     */
    private _validateRootDir(): void {
        if (this.root_dir.trim() === "") {
            this.root_dir = process.cwd();
        }

        const rootDir = path.resolve(this.root_dir);
        if (!fs.existsSync(rootDir) || !fs.statSync(rootDir).isDirectory()) {
            const msg = `Invalid root directory: ${this.root_dir} is not a directory.`;
            throw new Error(msg);
        }
        this.root_dir = rootDir;
    }

    /**
     * Validate the models configuration.
     */
    private _validateModels(): void {
        if (!(defs.DEFAULT_CHAT_MODEL_ID in this.models)) {
            throw new LanguageModelConfigMissingError(defs.DEFAULT_CHAT_MODEL_ID);
        }
        if (!(defs.DEFAULT_EMBEDDING_MODEL_ID in this.models)) {
            throw new LanguageModelConfigMissingError(defs.DEFAULT_EMBEDDING_MODEL_ID);
        }
    }

    /**
     * Validate the input file pattern based on the specified type.
     */
    private _validateInputPattern(): void {
        if (this.input.file_pattern.length === 0) {
            if (this.input.file_type === InputFileType.TEXT) {
                this.input.file_pattern = ".*\\.txt$";
            } else {
                this.input.file_pattern = `.*\\.${this.input.file_type}$`;
            }
        }
    }

    /**
     * Validate the input base directory.
     */
    private _validateInputBaseDir(): void {
        if (this.input.storage.type === StorageType.FILE) {
            if (this.input.storage.base_dir.trim() === "") {
                const msg = "input storage base directory is required for file input storage. Please rerun `graphrag init` and set the input storage configuration.";
                throw new Error(msg);
            }
            this.input.storage.base_dir = path.resolve(this.root_dir, this.input.storage.base_dir);
        }
    }

    /**
     * Validate the output base directory.
     */
    private _validateOutputBaseDir(): void {
        if (this.output.type === StorageType.FILE) {
            if (this.output.base_dir.trim() === "") {
                const msg = "output base directory is required for file output. Please rerun `graphrag init` and set the output configuration.";
                throw new Error(msg);
            }
            this.output.base_dir = path.resolve(this.root_dir, this.output.base_dir);
        }
    }

    /**
     * Validate the outputs dict base directories.
     */
    private _validateMultiOutputBaseDirs(): void {
        if (this.outputs) {
            for (const output of Object.values(this.outputs)) {
                if (output.type === StorageType.FILE) {
                    if (output.base_dir.trim() === "") {
                        const msg = "Output base directory is required for file output. Please rerun `graphrag init` and set the output configuration.";
                        throw new Error(msg);
                    }
                    output.base_dir = path.resolve(this.root_dir, output.base_dir);
                }
            }
        }
    }

    /**
     * Validate the update index output base directory.
     */
    private _validateUpdateIndexOutputBaseDir(): void {
        if (this.update_index_output.type === StorageType.FILE) {
            if (this.update_index_output.base_dir.trim() === "") {
                const msg = "update_index_output base directory is required for file output. Please rerun `graphrag init` and set the update_index_output configuration.";
                throw new Error(msg);
            }
            this.update_index_output.base_dir = path.resolve(this.root_dir, this.update_index_output.base_dir);
        }
    }

    /**
     * Validate the reporting base directory.
     */
    private _validateReportingBaseDir(): void {
        if (this.reporting.type === ReportingType.FILE) {
            if (this.reporting.base_dir.trim() === "") {
                const msg = "Reporting base directory is required for file reporting. Please rerun `graphrag init` and set the reporting configuration.";
                throw new Error(msg);
            }
            this.reporting.base_dir = path.resolve(this.root_dir, this.reporting.base_dir);
        }
    }

    /**
     * Validate the vector store configuration.
     */
    private _validateVectorStoreDbUri(): void {
        for (const store of Object.values(this.vector_store)) {
            if (store.type === "lancedb") {
                if (!store.db_uri || store.db_uri.trim() === "") {
                    const msg = "Vector store URI is required for LanceDB. Please rerun `graphrag init` and set the vector store configuration.";
                    throw new Error(msg);
                }
                store.db_uri = path.resolve(this.root_dir, store.db_uri);
            }
        }
    }

    /**
     * Get a model configuration by ID.
     */
    public getLanguageModelConfig(modelId: string): LanguageModelConfig {
        if (!(modelId in this.models)) {
            const errMsg = `Model ID ${modelId} not found in configuration. Please rerun \`graphrag init\` and set the model configuration.`;
            throw new Error(errMsg);
        }
        return this.models[modelId];
    }

    /**
     * Get a vector store configuration by ID.
     */
    public getVectorStoreConfig(vectorStoreId: string): VectorStoreConfig {
        if (!(vectorStoreId in this.vector_store)) {
            const errMsg = `Vector Store ID ${vectorStoreId} not found in configuration. Please rerun \`graphrag init\` and set the vector store configuration.`;
            throw new Error(errMsg);
        }
        return this.vector_store[vectorStoreId];
    }

    /**
     * Validate the model configuration.
     */
    private _validateModel(): void {
        this._validateRootDir();
        this._validateModels();
        this._validateInputPattern();
        this._validateInputBaseDir();
        this._validateReportingBaseDir();
        this._validateOutputBaseDir();
        this._validateMultiOutputBaseDirs();
        this._validateUpdateIndexOutputBaseDir();
        this._validateVectorStoreDbUri();
    }
}

/**
 * Create a GraphRagConfig from a configuration object.
 */
export function createGraphRagConfig(values: Record<string, any> = {}): GraphRagConfig {
    return new GraphRagConfig(values);
}