# 核心学习流程图

## 主流程架构

```mermaid
flowchart TD
    A[大模型] -->|调用| B[learning.ts]
    B -->|分析| C[检索聊天上下文]
    C -->|处理| D[智能思考分析]
    D -->|提取| E[总结成功经验]
    E -->|生成| F[获取技能流程]
    F -->|初始化| G[创建.Ai\Skills目录]
    G -->|保存| H[存储到.Ai\Skills]
    H -->|组织| I[按技能名称分类]
    
    J[遇到复杂情况] -->|查询| K[检索技能库]
    K -->|匹配| L[返回技能给大模型]
    L -->|执行| M[应用技能到代码]
    M -->|监控| N[获取技能反馈]
    N -->|优化| O[更新技能数据]
    O -->|循环| H
    
    %% 样式定义
    classDef llm fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef learning fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef application fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef feedback fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A llm
    class B,C,D,E,F learning
    class G,H,I storage
    class J,K,L,M application
    class N,O feedback
```

## 详细执行步骤

### 1. 学习阶段 (Learning Phase)
```mermaid
sequenceDiagram
    participant LLM as 大模型
    participant LT as learning.ts
    participant CA as 上下文分析器
    participant SE as 技能提取器
    participant FS as 文件系统
    
    Note over LLM,FS: 学习触发
    LLM->>+LT: 调用learning工具
    LT->>+CA: 检索聊天上下文
    CA->>CA: 分析成功模式
    CA->>-LT: 返回分析结果
    
    Note over LT,SE: 技能生成
    LT->>+SE: 智能思考分析
    SE->>SE: 总结成功经验
    SE->>SE: 获取技能流程
    SE->>-LT: 生成技能数据
    
    Note over LT,FS: 持久化存储
    LT->>+FS: 创建.Ai\Skills目录
    FS->>FS: 初始化目录结构
    LT->>FS: 存储技能到分类目录
    FS->>FS: 按技能名称组织
    FS->>-LT: 确认保存完成
    LT->>-LLM: 学习完成反馈
```

### 2. 应用阶段 (Application Phase)
```mermaid
sequenceDiagram
    participant LLM as 大模型
    participant LT as learning.ts
    participant SR as 技能检索器
    participant SE as 技能执行器
    participant FB as 反馈收集器
    
    Note over LLM,FB: 技能应用
    LLM->>+LT: 遇到复杂情况
    LT->>+SR: 检索技能库
    SR->>SR: 搜索匹配技能
    SR->>-LT: 返回相关技能
    
    Note over LT,SE: 技能执行
    LT->>+LLM: 返回技能给大模型
    LLM->>+SE: 应用技能到代码
    SE->>SE: 执行技能步骤
    SE->>-LLM: 返回执行结果
    
    Note over LLM,FB: 反馈优化
    LLM->>+FB: 获取技能反馈
    FB->>FB: 分析执行效果
    FB->>-LT: 反馈数据
    LT->>LT: 更新技能数据
    LT->>-LLM: 优化完成
```

## 核心组件交互

### 1. learning.ts 核心引擎
```mermaid
graph TD
    A[learning.ts核心] --> B[动作分发器]
    
    B --> C[学习模块]
    B --> D[存储模块] 
    B --> E[检索模块]
    B --> F[应用模块]
    B --> G[反馈模块]
    
    C --> C1[analyze分析]
    C --> C2[create_skill创建]
    C --> C3[incremental_update增量更新]
    
    D --> D1[init_skills_directory初始化]
    D --> D2[save_skill_to_file保存]
    D --> D3[backup_skills备份]
    
    E --> E1[search_skill搜索]
    E --> E2[load_skill_from_file加载]
    E --> E3[get_skill获取]
    
    F --> F1[compose_skills组合]
    F --> F2[form_intuition直觉]
    F --> F3[auto_evolve进化]
    
    G --> G1[update_skill更新]
    G --> G2[sync_skills同步]
    G --> G3[性能监控]
    
    classDef core fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef module fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef action fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px
    
    class A core
    class B,C,D,E,F,G module
    class C1,C2,C3,D1,D2,D3,E1,E2,E3,F1,F2,F3,G1,G2,G3 action
```

### 2. 技能存储结构
```mermaid
graph TD
    A[.Ai/Skills] --> B[core_skills/]
    A --> C[domain_skills/]
    A --> D[workflow_patterns/]
    A --> E[tool_combinations/]
    A --> F[skill_templates/]
    A --> G[learning_metrics/]
    A --> H[backups/]
    
    B --> B1["debugging_workflow.json<br/>错误恢复技能"]
    B --> B2["pattern_recognition.json<br/>模式识别技能"]
    
    C --> C1["frontend_dev.json<br/>前端开发技能"]
    C --> C2["backend_api.json<br/>后端API技能"]
    
    D --> D1["complex_solving.md<br/>复杂问题解决流程"]
    D --> D2["project_dev.md<br/>项目开发流程"]
    
    E --> E1["debug_toolchain.json<br/>调试工具链"]
    E --> E2["dev_toolchain.json<br/>开发工具链"]
    
    F --> F1["skill_template.md<br/>技能模板"]
    F --> F2["workflow_template.md<br/>流程模板"]
    
    classDef root fill:#fff3e0,stroke:#e65100,stroke-width:3px
    classDef category fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef skill fill:#f3e5f5,stroke:#4a148c,stroke-width:1px
    
    class A root
    class B,C,D,E,F,G,H category
    class B1,B2,C1,C2,D1,D2,E1,E2,F1,F2 skill
```

## 智能化特性流程

### 1. 自适应学习循环
```mermaid
graph LR
    A[问题解决成功] --> B[自动触发学习]
    B --> C[提取成功模式]
    C --> D[创建/更新技能]
    D --> E[存储到技能库]
    E --> F[下次遇到类似问题]
    F --> G[自动应用技能]
    G --> H[收集执行反馈]
    H --> I[优化技能质量]
    I --> D
    
    classDef trigger fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef storage fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef feedback fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A,B trigger
    class C,D,I process
    class E storage
    class F,G,H feedback
```

### 2. 复杂问题处理流程
```mermaid
graph TD
    A[复杂问题输入] --> B{技能库检索}
    B -->|找到单一技能| C[直接应用技能]
    B -->|找到多个技能| D[技能组合策略]
    B -->|未找到合适技能| E[分解问题]
    
    C --> F[执行技能步骤]
    D --> G[智能编排执行]
    E --> H[创建新技能]
    
    F --> I[收集执行结果]
    G --> I
    H --> I
    
    I --> J[反馈学习系统]
    J --> K[更新技能库]
    
    classDef input fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef execution fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef feedback fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A input
    class B decision
    class C,D,E,F,G,H execution
    class I,J,K feedback
```

## 性能优化机制

### 1. 技能质量管理
```mermaid
graph LR
    A[技能使用] --> B[性能监控]
    B --> C[成功率统计]
    C --> D{质量评估}
    D -->|优秀| E[提升权重]
    D -->|良好| F[保持现状]
    D -->|较差| G[标记优化]
    
    E --> H[优先推荐]
    F --> I[正常使用]
    G --> J[触发改进]
    
    J --> K[分析失败原因]
    K --> L[增量更新技能]
    L --> A
    
    classDef monitor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef evaluate fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef action fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef improve fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A,B,C monitor
    class D evaluate
    class E,F,G,H,I action
    class J,K,L improve
```

### 2. 缓存与索引优化
```mermaid
graph TD
    A[技能请求] --> B{缓存检查}
    B -->|命中| C[返回缓存结果]
    B -->|未命中| D[执行搜索]
    
    D --> E[关键词匹配]
    D --> F[语义相似度]
    D --> G[成功率排序]
    
    E --> H[结果合并]
    F --> H
    G --> H
    
    H --> I[更新缓存]
    I --> J[返回最佳匹配]
    C --> J
    
    classDef request fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef cache fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef search fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef result fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A request
    class B,C,I cache
    class D,E,F,G,H search
    class J result
```

这个优化的架构图更清晰地展示了你描述的核心流程：从大模型调用learning.ts开始，到技能应用和反馈更新的完整循环，突出了学习系统的智能化和自适应特性。