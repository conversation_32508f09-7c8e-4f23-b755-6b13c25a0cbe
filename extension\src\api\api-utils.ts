import { ApiHistoryItem } from "../agent/v1/main-agent"
import { ClaudeMessage, isV1ClaudeMessage, V1ClaudeMessage } from "../shared/messages/extension-message"
import { isTextBlock } from "../shared/format-tools"
import { ModelInfo } from "./providers/types"

export interface ApiMetrics {
	inputTokens: number
	outputTokens: number
	inputCacheRead: number
	inputCacheWrite: number
}

/**
 * Cleans up an Anthropic message, removing empty content blocks.
 * @param msg Anthropics message param
 * @returns Cleaned up message or null if empty
 */
export function cleanUpMsg(msg: ApiHistoryItem): ApiHistoryItem | null {
	if (typeof msg.content === "string" && msg.content.trim() === "") {
		return null
	}
	if (Array.isArray(msg.content)) {
		const newContent = msg.content.filter((block) => {
			if (isTextBlock(block)) {
				return block.text.trim() !== ""
			}
			return true
		})
		if (newContent.length === 0) {
			return null
		}
		return { ...msg, content: newContent }
	}
	return msg
}

/**
 * Retrieves and processes API metrics from conversation history
 * @param claudeMessages - Conversation history
 * @returns Processed API metrics
 */
export function getApiMetrics(claudeMessages: ClaudeMessage[]): ApiMetrics {
	const defaultMetrics: ApiMetrics = {
		inputTokens: 0,
		outputTokens: 0,
		inputCacheRead: 0,
		inputCacheWrite: 0,
	}
	const reversedMessages = claudeMessages.slice().reverse()
	const lastV1Message = reversedMessages.find((m) => isV1ClaudeMessage(m) && m?.apiMetrics)
	return (lastV1Message as V1ClaudeMessage)?.apiMetrics || defaultMetrics
}
