/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing chunk strategies.
 */

import { ChunkingConfig } from '../../../config/models/chunking-config';
import { TextChunk } from './typing';
import { Tokenizer, splitMultipleTextsOnTokens } from '../../text-splitting/text-splitting';

/**
 * Get encoding functions for a given encoding name.
 * Note: This is a simplified implementation. In production, use tiktoken-js.
 */
function getEncodingFn(encodingName: string): [
    (text: string) => number[],
    (tokens: number[]) => string
] {
    // Simplified encoding - in reality you'd use tiktoken-js
    const encode = (text: string): number[] => {
        if (typeof text !== 'string') {
            text = String(text);
        }
        // Simple approximation: convert each character to a token ID
        const tokens: number[] = [];
        for (let i = 0; i < text.length; i += 4) {
            tokens.push(Math.floor(Math.random() * 50000));
        }
        return tokens;
    };

    const decode = (tokens: number[]): string => {
        // Simplified decoding
        return `[DECODED_${tokens.length}_TOKENS]`;
    };

    return [encode, decode];
}

/**
 * Chunks text into chunks based on encoding tokens.
 */
export function* runTokens(
    input: string[],
    config: ChunkingConfig,
    tick: (increment: number) => void
): Iterable<TextChunk> {
    const tokensPerChunk = config.size;
    const chunkOverlap = config.overlap;
    const encodingName = config.encodingModel;

    const [encode, decode] = getEncodingFn(encodingName);
    
    const tokenizer: Tokenizer = {
        chunkOverlap: chunkOverlap,
        tokensPerChunk: tokensPerChunk,
        encode: encode,
        decode: decode,
    };

    const chunks = splitMultipleTextsOnTokens(input, tokenizer, tick);
    
    for (const chunk of chunks) {
        yield {
            textChunk: chunk.text,
            sourceDocIndices: chunk.docIndices,
            nTokens: chunk.tokenCount
        };
    }
}

/**
 * Chunks text into multiple parts by sentence.
 * Note: This is a simplified implementation. In production, use a proper sentence tokenizer.
 */
export function* runSentences(
    input: string[],
    _config: ChunkingConfig,
    tick: (increment: number) => void
): Iterable<TextChunk> {
    for (let docIdx = 0; docIdx < input.length; docIdx++) {
        const text = input[docIdx];
        
        // Simple sentence splitting - in reality you'd use a proper sentence tokenizer
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        
        for (const sentence of sentences) {
            yield {
                textChunk: sentence.trim(),
                sourceDocIndices: [docIdx],
            };
        }
        
        tick(1);
    }
}