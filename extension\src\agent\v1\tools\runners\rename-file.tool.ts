import * as fs from "fs/promises"
import * as path from "path"
import { BaseAgentTool } from "../base-agent.tool"
import { RenameFileToolParams } from "../schema/rename_file"
import { getReadablePath } from "../../utils"

export class RenameFileTool extends BaseAgentTool<RenameFileToolParams> {
	async execute() {
		const { input } = this.params
		const { path: filePath, new_name, dry_run = false } = input
		
		// Validate required parameters
		if (!filePath || typeof filePath !== 'string' || filePath.trim() === '') {
			const errorMsg = `
			<rename_file_response>
				<status>
					<r>error</r>
					<operation>rename_file</operation>
					<timestamp>${new Date().toISOString()}</timestamp>
				</status>
				<error_details>
					<type>invalid_parameter</type>
					<message>File path is required and must be a non-empty string</message>
					<provided_path>${String(filePath || 'null')}</provided_path>
				</error_details>
			</rename_file_response>`
			return this.toolResponse("error", errorMsg)
		}

		if (!new_name || typeof new_name !== 'string' || new_name.trim() === '') {
			const errorMsg = `
			<rename_file_response>
				<status>
					<r>error</r>
					<operation>rename_file</operation>
					<timestamp>${new Date().toISOString()}</timestamp>
				</status>
				<error_details>
					<type>invalid_parameter</type>
					<message>New name is required and must be a non-empty string</message>
					<provided_new_name>${String(new_name || 'null')}</provided_new_name>
				</error_details>
			</rename_file_response>`
			return this.toolResponse("error", errorMsg)
		}

		try {
			const absolutePath = path.resolve(this.cwd, filePath)
			const directory = path.dirname(absolutePath)
			const newAbsolutePath = path.join(directory, new_name)
			const newPath = path.join(path.dirname(filePath), new_name)

			// Check if source exists
			try {
				await fs.access(absolutePath)
			} catch {
				const errorMsg = `
				<rename_file_response>
					<status>
						<r>error</r>
						<operation>rename_file</operation>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<error_details>
						<type>file_not_found</type>
						<message>Source file or directory does not exist</message>
						<path>${getReadablePath(filePath, this.cwd)}</path>
					</error_details>
				</rename_file_response>`
				return this.toolResponse("error", errorMsg)
			}

			// Check if destination already exists
			try {
				await fs.access(newAbsolutePath)
				const errorMsg = `
				<rename_file_response>
					<status>
						<r>error</r>
						<operation>rename_file</operation>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<error_details>
						<type>destination_exists</type>
						<message>A file or directory with the new name already exists</message>
						<path>${getReadablePath(filePath, this.cwd)}</path>
						<new_name>${new_name}</new_name>
						<new_path>${getReadablePath(newPath, this.cwd)}</new_path>
					</error_details>
				</rename_file_response>`
				return this.toolResponse("error", errorMsg)
			} catch {
				// Destination doesn't exist, which is what we want
			}

			// Perform the rename operation (unless dry run)
			if (!dry_run) {
				await fs.rename(absolutePath, newAbsolutePath)
			}

			// Success response
			return this.toolResponse(
				"success",
				`<rename_file_response>
					<status>
						<r>success</r>
						<operation>rename_file</operation>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<details>
						<old_path>${getReadablePath(filePath, this.cwd)}</old_path>
						<new_name>${new_name}</new_name>
						<new_path>${getReadablePath(newPath, this.cwd)}</new_path>
						<dry_run>${dry_run}</dry_run>
						<message>${dry_run ? 'Preview: ' : ''}File renamed successfully</message>
					</details>
				</rename_file_response>`
			)

		} catch (error) {
			const errorMsg = `
			<rename_file_response>
				<status>
					<r>error</r>
					<operation>rename_file</operation>
					<timestamp>${new Date().toISOString()}</timestamp>
				</status>
				<error_details>
					<type>operation_failed</type>
					<message>${error instanceof Error ? error.message : 'Unknown error occurred'}</message>
					<path>${getReadablePath(filePath, this.cwd)}</path>
					<new_name>${new_name}</new_name>
				</error_details>
			</rename_file_response>`
			return this.toolResponse("error", errorMsg)
		}
	}
}