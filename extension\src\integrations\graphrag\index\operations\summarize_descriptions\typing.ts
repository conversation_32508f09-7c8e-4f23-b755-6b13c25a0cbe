/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'SummarizedDescriptionResult' model.
 */

import { PipelineCache } from '../../../cache/pipeline-cache';

export type StrategyConfig = Record<string, any>;

/**
 * Entity summarization result class definition.
 */
export interface SummarizedDescriptionResult {
    id: string | [string, string];
    description: string;
}

/**
 * Summarization strategy function type
 */
export type SummarizationStrategy = (
    id: string | [string, string],
    descriptions: string[],
    cache: PipelineCache,
    config: StrategyConfig
) => Promise<SummarizedDescriptionResult>;

/**
 * Description summarize row interface.
 */
export interface DescriptionSummarizeRow {
    graph: any;
}

/**
 * SummarizeStrategyType enum definition.
 */
export enum SummarizeStrategyType {
    graph_intelligence = "graph_intelligence"
}