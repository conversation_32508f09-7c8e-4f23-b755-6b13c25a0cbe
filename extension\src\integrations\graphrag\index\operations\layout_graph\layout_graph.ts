/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing layout_graph, _run_layout and _apply_layout_to_graph methods definition.
 */

import { DataFrame } from '../../../data-model/types';
import { Graph } from '../../utils/graphs';
import { NodeEmbeddings } from '../embed-graph/typing';
import { GraphLayout } from './typing';
import { run as runUmap } from './umap';
import { run as run<PERSON>ero } from './zero';

const logger = console;

/**
 * Apply a layout algorithm to a Graph. The method returns a dataframe containing the node positions.
 * 
 * ## Usage
 * ```yaml
 * args:
 *     graph: The Graph to layout
 *     embeddings: Embeddings for each node in the graph
 *     strategy: <strategy config> # See strategies section below
 * ```
 * 
 * ## Strategies
 * The layout graph verb uses a strategy to layout the graph. The strategy is a json object which defines the strategy to use. The following strategies are available:
 * 
 * ### umap
 * This strategy uses the umap algorithm to layout a graph. The strategy config is as follows:
 * ```yaml
 * strategy:
 *     type: umap
 *     n_neighbors: 5 # Optional, The number of neighbors to use for the umap algorithm, default: 5
 *     min_dist: 0.75 # Optional, The min distance to use for the umap algorithm, default: 0.75
 * ```
 */
export function layoutGraph(
    graph: Graph,
    enabled: boolean,
    embeddings?: NodeEmbeddings
): DataFrame {
    const layout = runLayout(
        graph,
        enabled,
        embeddings || {}
    );

    const layoutData = layout.map(position => ({
        label: position.label,
        x: position.x,
        y: position.y,
        size: position.size
    }));

    return {
        columns: ['label', 'x', 'y', 'size'],
        data: layoutData
    };
}

/**
 * Run layout algorithm based on enabled flag.
 */
function runLayout(
    graph: Graph,
    enabled: boolean,
    embeddings: NodeEmbeddings
): GraphLayout {
    if (enabled) {
        return runUmap(
            graph,
            embeddings,
            (error, stack, details) => {
                logger.error('Error in Umap', error, { stack, details });
            }
        );
    }
    
    return runZero(
        graph,
        (error, stack, details) => {
            logger.error('Error in Zero', error, { stack, details });
        }
    );
}