// providers/google-genai.ts
import { ProviderConfig } from "../types"
import { DEFAULT_BASE_URLS, PROVIDER_IDS, PROVIDER_NAMES } from "../constants"

export const googleGenAIConfig: ProviderConfig = {
	id: PROVIDER_IDS.GOOGLE_GENAI,
	name: PROVIDER_NAMES[PROVIDER_IDS.GOOGLE_GENAI],
	baseUrl: DEFAULT_BASE_URLS[PROVIDER_IDS.GOOGLE_GENAI],
	models: [
		{
			id: "gemini-2.5-pro",
			name: "Gemini 2.5 Pro (AI Studio)",
			contextWindow: 2_097_152,
			maxTokens: 8192,
			supportsImages: true,
			inputPrice: 0.5,
			outputPrice: 2.0,
			cacheReadsPrice: 0.05,
			cacheWritesPrice: 0.625,
			supportsPromptCache: true,
			provider: PROVIDER_IDS.GOOGLE_GENAI,
		},
	],
	requiredFields: ["apiKey"],
}
