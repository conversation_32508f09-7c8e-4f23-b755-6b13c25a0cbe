// providers/openai.ts
import { ProviderConfig } from "../types"
import { DEFAULT_BASE_URLS, PROVIDER_IDS, PROVIDER_NAMES } from "../constants"

export const openaiConfig: ProviderConfig = {
	id: PROVIDER_IDS.OPENAI,
	name: PROVIDER_NAMES[PROVIDER_IDS.OPENAI],
	baseUrl: DEFAULT_BASE_URLS[PROVIDER_IDS.OPENAI],
	models: [
		{
			id: "o3-mini-high",
			name: "O3 Mini (High Reasoning)",
			contextWindow: 200_000,
			maxTokens: 100_000,
			supportsImages: false,
			supportsPromptCache: true,
			inputPrice: 1.1,
			outputPrice: 4.4,
			cacheReadsPrice: 1.1 * 0.5, // 50% of input price
			cacheWritesPrice: 1.1,
			provider: PROVIDER_IDS.OPENAI,
			isThinkingModel: true,
			reasoningEffort: "high",
		},
		{
			id: "o3-mini-medium",
			name: "O3 Mini (Medium Reasoning)",
			contextWindow: 200_000,
			maxTokens: 100_000,
			supportsImages: false,
			supportsPromptCache: true,
			inputPrice: 1.1,
			outputPrice: 4.4,
			cacheReadsPrice: 1.1 * 0.5, // 50% of input price
			cacheWritesPrice: 1.1,
			provider: PROVIDER_IDS.OPENAI,
			isThinkingModel: true,
			reasoningEffort: "medium",
		},
		{
			id: "o1",
			name: "O1",
			contextWindow: 200_000,
			maxTokens: 100_000,
			supportsImages: true,
			supportsPromptCache: true,
			inputPrice: 15.0,
			outputPrice: 60.0,
			cacheReadsPrice: 15.0 * 0.5, // 50% of input price
			provider: PROVIDER_IDS.OPENAI,
			cacheWritesPrice: 15.0,
			isThinkingModel: true,
		},
		{
			id: "o1-preview",
			name: "O1 Preview",
			contextWindow: 128000,
			maxTokens: 32768,
			supportsImages: true,
			supportsPromptCache: true,
			inputPrice: 15.0,
			outputPrice: 60.0,
			cacheReadsPrice: 15.0 * 0.5, // 50% of input price
			provider: PROVIDER_IDS.OPENAI,
			cacheWritesPrice: 15.0,
			isThinkingModel: true,
		},
		{
			id: "o1-mini",
			name: "O1 Mini",
			contextWindow: 128000,
			maxTokens: 65536,
			supportsImages: true,
			supportsPromptCache: true,
			inputPrice: 3.0,
			outputPrice: 12.0,
			cacheReadsPrice: 3.0 * 0.5, // 50% of input price
			provider: PROVIDER_IDS.OPENAI,
			cacheWritesPrice: 3.0,
			isThinkingModel: true,
		},
		{
			id: "gpt-4o",
			name: "GPT-4 O",
			contextWindow: 128000,
			maxTokens: 4096,
			supportsImages: true,
			supportsPromptCache: true,
			inputPrice: 5.0,
			outputPrice: 15.0,
			cacheReadsPrice: 5.0 * 0.5, // 50% of input price
			provider: PROVIDER_IDS.OPENAI,
			cacheWritesPrice: 5.0,
		},
		{
			id: "gpt-4o-mini",
			name: "GPT-4 O Mini",
			contextWindow: 128000,
			maxTokens: 16384,
			supportsImages: true,
			supportsPromptCache: true,
			inputPrice: 0.15,
			outputPrice: 0.6,
			provider: PROVIDER_IDS.OPENAI,
			cacheWritesPrice: 0.15,
			cacheReadsPrice: 0.15 * 0.5, // 50% of input price
		},
		{
			id: "gpt-4.1-turbo",
			name: "GPT-4.1 Turbo",
			contextWindow: 128000,
			maxTokens: 4096,
			supportsImages: true,
			supportsPromptCache: true,
			inputPrice: 10.0,
			outputPrice: 30.0,
			provider: PROVIDER_IDS.OPENAI,
			cacheWritesPrice: 10.0,
			cacheReadsPrice: 10.0 * 0.5, // 50% of input price
		},
	],
	requiredFields: ["apiKey"],
}
