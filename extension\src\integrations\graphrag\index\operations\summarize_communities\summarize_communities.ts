/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing create_community_reports and load_strategy methods definition.
 */

import { DataFrame } from '../../../data-model/types';
import { PipelineCache } from '../../../cache/pipeline-cache';
import { WorkflowCallbacks } from '../../../callbacks/workflow-callbacks';
import { NoopWorkflowCallbacks } from '../../../callbacks/noop-workflow-callbacks';
import { AsyncType } from '../../../config/enums';
import { deriveFromRows } from '../../utils/derive-from-rows';
import { 
    CommunityReport, 
    CommunityReportsStrategy, 
    CreateCommunityReportsStrategyType 
} from './typing';
import { getLevels } from './utils';
import { runGraphIntelligence } from './strategies';
import { COMMUNITY_ID, COMMUNITY_LEVEL, CONTEXT_STRING } from '../../../data-model/schemas';

const logger = console;

/**
 * Generate community summaries.
 */
export async function summarizeCommunities(
    nodes: DataFrame,
    communities: DataFrame,
    localContexts: DataFrame,
    levelContextBuilder: (
        reports: DataFrame,
        communityHierarchyDF: DataFrame,
        localContextDF: DataFrame,
        level: number,
        maxContextTokens: number
    ) => DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    strategy: Record<string, any>,
    maxInputLength: number,
    asyncMode: AsyncType = AsyncType.AsyncIO,
    numThreads: number = 4
): Promise<DataFrame> {
    const reports: Array<CommunityReport | null> = [];
    let completed = 0;
    const totalContexts = localContexts.data.length;
    
    const tick = () => {
        completed += 1;
        if (callbacks.progress) {
            callbacks.progress(completed / totalContexts, 'Generating community reports');
        }
    };

    const strategyExec = loadStrategy(strategy.type);
    const strategyConfig = { ...strategy };

    // Create community hierarchy
    const communityHierarchy = createCommunityHierarchy(communities);

    const levels = getLevels(nodes);
    const levelContexts: DataFrame[] = [];

    for (const level of levels) {
        const reportsDF: DataFrame = {
            columns: ['community', 'title', 'summary', 'fullContent', 'rank', 'level'],
            data: reports.filter(r => r !== null).map(r => r!)
        };

        const levelContext = levelContextBuilder(
            reportsDF,
            communityHierarchy,
            localContexts,
            level,
            maxInputLength
        );
        levelContexts.push(levelContext);
    }

    for (let i = 0; i < levelContexts.length; i++) {
        const levelContext = levelContexts[i];

        async function runGenerate(record: Record<string, any>): Promise<CommunityReport | null> {
            const result = await generateReport(
                strategyExec,
                record[COMMUNITY_ID],
                record[COMMUNITY_LEVEL],
                record[CONTEXT_STRING],
                callbacks,
                cache,
                strategyConfig
            );
            tick();
            return result;
        }

        const localReports = await deriveFromRows(
            levelContext,
            runGenerate,
            new NoopWorkflowCallbacks(),
            numThreads,
            asyncMode,
            `level ${levels[i]} summarize communities progress: `
        );

        reports.push(...localReports.filter(lr => lr !== null));
    }

    // Convert reports to DataFrame
    const validReports = reports.filter(r => r !== null) as CommunityReport[];
    const reportData = validReports.map(report => ({
        community: report.community,
        title: report.title,
        summary: report.summary,
        full_content: report.fullContent,
        full_content_json: report.fullContentJson,
        rank: report.rank,
        level: report.level,
        rating_explanation: report.ratingExplanation,
        findings: report.findings
    }));

    return {
        columns: [
            'community', 'title', 'summary', 'full_content', 'full_content_json',
            'rank', 'level', 'rating_explanation', 'findings'
        ],
        data: reportData
    };
}

/**
 * Generate a report for a single community.
 */
async function generateReport(
    runner: CommunityReportsStrategy,
    communityId: number,
    communityLevel: number,
    communityContext: string,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    strategy: Record<string, any>
): Promise<CommunityReport | null> {
    return await runner(
        communityId,
        communityContext,
        communityLevel,
        callbacks,
        cache,
        strategy
    );
}

/**
 * Load strategy method definition.
 */
function loadStrategy(strategy: CreateCommunityReportsStrategyType): CommunityReportsStrategy {
    switch (strategy) {
        case CreateCommunityReportsStrategyType.graph_intelligence:
            return runGraphIntelligence;
        default:
            throw new Error(`Unknown strategy: ${strategy}`);
    }
}

/**
 * Create community hierarchy DataFrame.
 */
function createCommunityHierarchy(communities: DataFrame): DataFrame {
    const hierarchyData: any[] = [];
    
    communities.data.forEach(row => {
        if (row.children && Array.isArray(row.children)) {
            row.children.forEach((child: any) => {
                hierarchyData.push({
                    community: row.community,
                    level: row.level,
                    sub_community: child
                });
            });
        }
    });

    return {
        columns: ['community', 'level', 'sub_community'],
        data: hierarchyData.filter(row => row.sub_community != null)
    };
}