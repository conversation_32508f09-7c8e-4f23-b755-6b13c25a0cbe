/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing load method definition.
 */

import { DataFrame } from '../../data-model/types';
import { InputConfig } from '../../config/models/input-config';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { loadFiles, processDataColumns } from './util';

const logger = console;

/**
 * Load json inputs from a directory.
 * @param config - Input configuration
 * @param storage - Pipeline storage instance
 * @returns Promise resolving to DataFrame with JSON data
 */
export async function loadJson(
    config: InputConfig,
    storage: PipelineStorage,
): Promise<DataFrame> {
    logger.info(`Loading json files from ${config.storage.baseDir}`);

    async function loadFile(path: string, group?: Record<string, any>): Promise<DataFrame> {
        const groupData = group || {};
        
        const text = await storage.get(path, { encoding: config.encoding });
        const asJson = JSON.parse(text);
        
        // JSON file could just be a single object, or an array of objects
        const rows = Array.isArray(asJson) ? asJson : [asJson];
        
        // Extract all unique columns from the data
        const allColumns = new Set<string>();
        rows.forEach(row => {
            Object.keys(row).forEach(key => allColumns.add(key));
        });
        
        // Add group keys to columns
        Object.keys(groupData).forEach(key => allColumns.add(key));
        
        const data = rows.map(row => {
            const newRow = { ...row };
            
            // Add group data
            const additionalKeys = Object.keys(groupData);
            additionalKeys.forEach(key => {
                newRow[key] = groupData[key];
            });
            
            return newRow;
        });

        let result: DataFrame = {
            columns: Array.from(allColumns),
            data: data
        };

        result = processDataColumns(result, config, path);

        const creationDate = await storage.getCreationDate(path);
        result.data = result.data.map(row => ({
            ...row,
            creation_date: creationDate
        }));

        return result;
    }

    return await loadFiles(loadFile, config, storage);
}