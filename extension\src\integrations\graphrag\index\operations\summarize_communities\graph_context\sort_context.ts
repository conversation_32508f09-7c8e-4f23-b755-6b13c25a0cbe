// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Sort context by degree in descending order.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';
import { numTokens } from '../../../../query/llm/text-utils.js';

/**
 * Sort context by degree in descending order, optimizing for performance.
 */
export function sortContext(
  localContext: Record<string, any>[],
  subCommunityReports?: Record<string, any>[],
  maxContextTokens?: number,
  nodeNameColumn: string = schemas.TITLE,
  nodeDetailsColumn: string = schemas.NODE_DETAILS,
  edgeIdColumn: string = schemas.SHORT_ID,
  edgeDetailsColumn: string = schemas.EDGE_DETAILS,
  edgeDegreeColumn: string = schemas.EDGE_DEGREE,
  edgeSourceColumn: string = schemas.EDGE_SOURCE,
  edgeTargetColumn: string = schemas.EDGE_TARGET,
  claimDetailsColumn: string = schemas.CLAIM_DETAILS
): string {
  /**
   * Concatenate structured data into a context string.
   */
  function getContextString(
    entities: Record<string, any>[],
    edges: Record<string, any>[],
    claims: Record<string, any>[],
    subCommunityReports?: Record<string, any>[]
  ): string {
    const contexts: string[] = [];
    
    if (subCommunityReports && subCommunityReports.length > 0) {
      const reportDf = DataFrame.fromRecords(subCommunityReports);
      if (!reportDf.isEmpty()) {
        contexts.push(`----Reports-----\n${reportDf.toCsv()}`);
      }
    }

    const sections = [
      ['Entities', entities],
      ['Claims', claims],
      ['Relationships', edges],
    ] as const;

    for (const [label, data] of sections) {
      if (data && data.length > 0) {
        const dataDf = DataFrame.fromRecords(data);
        if (!dataDf.isEmpty()) {
          contexts.push(`-----${label}-----\n${dataDf.toCsv()}`);
        }
      }
    }

    return contexts.join('\n\n');
  }

  // Preprocess local context
  const edges: Record<string, any>[] = [];
  for (const record of localContext) {
    const edgeDetails = record[edgeDetailsColumn];
    if (Array.isArray(edgeDetails)) {
      for (const e of edgeDetails) {
        if (typeof e === 'object' && e !== null) {
          edges.push({
            ...e,
            [schemas.SHORT_ID]: parseInt(e[schemas.SHORT_ID])
          });
        }
      }
    }
  }

  const nodeDetails: Record<string, any> = {};
  for (const record of localContext) {
    const nodeName = record[nodeNameColumn];
    const nodeDetail = record[nodeDetailsColumn];
    if (nodeDetail) {
      nodeDetails[nodeName] = {
        ...nodeDetail,
        [schemas.SHORT_ID]: parseInt(nodeDetail[schemas.SHORT_ID])
      };
    }
  }

  const claimDetails: Record<string, any[]> = {};
  for (const record of localContext) {
    const nodeName = record[nodeNameColumn];
    const claims = record[claimDetailsColumn];
    if (Array.isArray(claims)) {
      claimDetails[nodeName] = claims
        .filter(c => typeof c === 'object' && c !== null && c[schemas.SHORT_ID] != null)
        .map(c => ({
          ...c,
          [schemas.SHORT_ID]: parseInt(c[schemas.SHORT_ID])
        }));
    }
  }

  // Sort edges by degree (desc) and ID (asc)
  edges.sort((a, b) => {
    const degreeA = a[edgeDegreeColumn] || 0;
    const degreeB = b[edgeDegreeColumn] || 0;
    if (degreeB !== degreeA) {
      return degreeB - degreeA;
    }
    const idA = a[edgeIdColumn] || '';
    const idB = b[edgeIdColumn] || '';
    return idA.toString().localeCompare(idB.toString());
  });

  // Deduplicate and build context incrementally
  const edgeIds = new Set<number>();
  const nodeIds = new Set<number>();
  const claimIds = new Set<number>();
  const sortedEdges: Record<string, any>[] = [];
  const sortedNodes: Record<string, any>[] = [];
  const sortedClaims: Record<string, any>[] = [];
  let contextString = '';

  for (const edge of edges) {
    const source = edge[edgeSourceColumn];
    const target = edge[edgeTargetColumn];

    // Add source and target node details
    for (const nodeName of [source, target]) {
      const node = nodeDetails[nodeName];
      if (node && !nodeIds.has(node[schemas.SHORT_ID])) {
        nodeIds.add(node[schemas.SHORT_ID]);
        sortedNodes.push(node);
      }
    }

    // Add claims related to source and target
    for (const nodeName of [source, target]) {
      const claims = claimDetails[nodeName];
      if (claims) {
        for (const claim of claims) {
          if (!claimIds.has(claim[schemas.SHORT_ID])) {
            claimIds.add(claim[schemas.SHORT_ID]);
            sortedClaims.push(claim);
          }
        }
      }
    }

    // Add the edge
    if (!edgeIds.has(edge[schemas.SHORT_ID])) {
      edgeIds.add(edge[schemas.SHORT_ID]);
      sortedEdges.push(edge);
    }

    // Generate new context string
    const newContextString = getContextString(
      sortedNodes,
      sortedEdges,
      sortedClaims,
      subCommunityReports
    );
    
    if (maxContextTokens && numTokens(newContextString) > maxContextTokens) {
      break;
    }
    contextString = newContextString;
  }

  // Return the final context string
  return contextString || getContextString(
    sortedNodes,
    sortedEdges,
    sortedClaims,
    subCommunityReports
  );
}

/**
 * Calculate context using parallelization if enabled.
 */
export function parallelSortContextBatch(
  communityDf: DataFrame,
  maxContextTokens: number,
  parallel: boolean = false
): DataFrame {
  let contextStrings: string[];

  if (parallel) {
    // Use Promise.all for parallel execution in TypeScript
    const allContexts = communityDf.getColumn(schemas.ALL_CONTEXT);
    const promises = allContexts.map(context =>
      Promise.resolve(sortContext(context, undefined, maxContextTokens))
    );
    
    contextStrings = await Promise.all(promises);
  } else {
    // Process sequentially
    const allContexts = communityDf.getColumn(schemas.ALL_CONTEXT);
    contextStrings = allContexts.map(contextList =>
      sortContext(contextList, undefined, maxContextTokens)
    );
  }

  // Create new DataFrame with context strings
  let result = communityDf.withColumn(schemas.CONTEXT_STRING, contextStrings);

  // Calculate other columns
  result = result
    .withColumn(
      schemas.CONTEXT_SIZE,
      result.getColumn(schemas.CONTEXT_STRING).map(numTokens)
    )
    .withColumn(
      schemas.CONTEXT_EXCEED_FLAG,
      result.getColumn(schemas.CONTEXT_SIZE).map(size => size > maxContextTokens)
    );

  return result;
}
