// schema/move_file.ts
import { z } from "zod"

/**
 * @tool move_file
 * @description SINGLE FILE MOVE/RENAME TOOL - For moving or renaming ONE file or directory
 * 
 * This tool is specifically designed for single file/directory move/rename operations.
 * Use this when you need to move or rename exactly ONE file or directory.
 * Destination directories are created automatically if they don't exist.
 * 
 * SINGLE FILE MODE: For moving/renaming ONE file/directory
 *    Parameters: source, destination, overwrite (optional), dry_run (optional)
 *    Use case: Move one specific file to a new location or rename it
 *    Benefits: Simple, focused, clear intent
 * 
 * PREVIEW FUNCTIONALITY (dry_run):
 * - Set dry_run=true to preview move without actually moving the file
 * - Shows source and destination paths
 * - Identifies potential conflicts and overwrite situations
 * - Validates destination directory creation requirements
 * 
 * OVERWRITE HANDLING:
 * - overwrite=false: Fail if destination exists (default)
 * - overwrite=true: Replace existing file
 * - Preview mode shows potential overwrite conflicts
 * 
 * FEATURES:
 * - Automatic directory creation for destination path
 * - Distinguishes between move and rename operations
 * - Clear error messages for permission issues
 * - Path validation and security checks
 * 
 * WHEN TO USE:
 * - Use this tool when you need to move or rename exactly ONE file or directory
 * - For moving a file to a different directory
 * - For renaming a file in the same directory
 * - For reorganizing single files in your project
 * 
 * DO NOT USE for multiple files - use batch_move_files instead
 * - COMMA-LIST: Quick batch moving with comma-separated file paths
 * @example (SINGLE FILE MODE - Move one file)
 * ```xml
 * <tool name="move_file">
 *   <source>src/old_location/file.js</source>
 *   <destination>src/new_location/file.js</destination>
 * </tool>
 * ```
 * @example (SINGLE FILE MODE - Rename with path change)
 * ```xml
 * <tool name="move_file">
 *   <source>src/oldname.js</source>
 *   <destination>src/components/NewName.js</destination>
 * </tool>
 * ```
 * @example (BATCH MODE - Reorganize multiple components)
 * ```xml
 * <tool name="move_file">
 *   <files>
 *     <file>
 *       <source>src/Button.tsx</source>
 *       <destination>src/components/ui/Button.tsx</destination>
 *     </file>
 *     <file>
 *       <source>src/Input.tsx</source>
 *       <destination>src/components/forms/Input.tsx</destination>
 *     </file>
 *     <file>
 *       <source>src/Modal.tsx</source>
 *       <destination>src/components/ui/Modal.tsx</destination>
 *     </file>
 *   </files>
 * </tool>
 * ```
 * @example (BATCH MODE - Move utilities to lib folder)
 * ```xml
 * <tool name="move_file">
 *   <files>
 *     <file>
 *       <source>utils/helper.js</source>
 *       <destination>src/lib/helper.js</destination>
 *     </file>
 *     <file>
 *       <source>utils/constants.js</source>
 *       <destination>src/lib/constants.js</destination>
 *       <overwrite>true</overwrite>
 *     </file>
 *   </files>
 * </tool>
 * ```
 */
const schema = z.object({
    source: z.string().describe("The current path of the file or directory to move (relative to the current working directory)."),
    destination: z.string().describe("The new path where the file or directory should be moved (relative to the current working directory)."),
    overwrite: z.boolean().optional().describe("Whether to overwrite the destination if it already exists. Defaults to false."),
    dry_run: z.boolean().optional().describe("Preview move without applying it. Defaults to false."),
})

const examples = [
    // Single file examples
    `<move_file>
  <source>src/components/old_component.tsx</source>
  <destination>src/components/ui/new_component.tsx</destination>
</move_file>`,

    `<move_file>
  <source>docs/readme.md</source>
  <destination>docs/README.md</destination>
  <dry_run>true</dry_run>
</move_file>`,

    `<move_file>
  <source>src/utils/helper.js</source>
  <destination>src/lib/helper.js</destination>
  <overwrite>true</overwrite>
</move_file>`,

    // Directory move
    `<move_file>
  <source>src/old_folder</source>
  <destination>src/new_folder</destination>
</move_file>`,
]

export const moveFileTool = {
    schema: {
        name: "move_file",
        schema,
    },
    examples,
}

export type MoveFileToolParams = {
    name: "move_file"
    input: z.infer<typeof schema>
}