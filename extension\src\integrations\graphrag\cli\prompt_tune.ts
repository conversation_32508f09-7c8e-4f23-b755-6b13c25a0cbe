/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * CLI implementation of the prompt-tune subcommand.
 */

import * as path from 'path';
import * as fs from 'fs';
import * as api from '../api';
import { ReportingType } from '../config/enums';
import { loadConfig } from '../config/load-config';
import {
    COMMUNITY_SUMMARIZATION_FILENAME,
} from '../prompt_tune/generator/community-report-summarization';
import {
    ENTITY_SUMMARIZATION_FILENAME,
} from '../prompt_tune/generator/entity-summarization-prompt';
import {
    EXTRACT_GRAPH_FILENAME,
} from '../prompt_tune/generator/extract-graph-prompt';
import { redact } from '../utils/cli';

const logger = console;

export interface PromptTuneOptions {
    root: string;
    config?: string;
    domain?: string;
    verbose: boolean;
    selectionMethod: api.DocSelectionType;
    limit: number;
    maxTokens: number;
    chunkSize: number;
    overlap: number;
    language?: string;
    discoverEntityTypes: boolean;
    output: string;
    nSubsetMax: number;
    k: number;
    minExamplesRequired: number;
}

/**
 * Prompt tune the model.
 */
export async function promptTune(options: PromptTuneOptions): Promise<void> {
    const rootPath = path.resolve(options.root);
    const graphConfig = loadConfig(rootPath, options.config);

    // Override chunking config in the configuration
    if (options.chunkSize !== graphConfig.chunks.size) {
        graphConfig.chunks.size = options.chunkSize;
    }

    if (options.overlap !== graphConfig.chunks.overlap) {
        graphConfig.chunks.overlap = options.overlap;
    }

    // Configure the root logger with the specified log level
    // Note: Logger initialization would be handled by the logging system

    // Log the configuration details
    if (graphConfig.reporting.type === ReportingType.file) {
        const logDir = path.join(rootPath, graphConfig.reporting.baseDir || '');
        const logPath = path.join(logDir, 'logs.txt');
        logger.info(`Logging enabled at ${logPath}`);
    } else {
        logger.info(`Logging not enabled for config ${redact(graphConfig)}`);
    }

    const prompts = await api.generateIndexingPrompts({
        config: graphConfig,
        chunkSize: options.chunkSize,
        overlap: options.overlap,
        limit: options.limit,
        selectionMethod: options.selectionMethod,
        domain: options.domain,
        language: options.language,
        maxTokens: options.maxTokens,
        discoverEntityTypes: options.discoverEntityTypes,
        minExamplesRequired: options.minExamplesRequired,
        nSubsetMax: options.nSubsetMax,
        k: options.k,
    });

    const outputPath = path.resolve(options.output);
    if (outputPath) {
        logger.info(`Writing prompts to ${outputPath}`);
        
        if (!fs.existsSync(outputPath)) {
            fs.mkdirSync(outputPath, { recursive: true });
        }
        
        const extractGraphPromptPath = path.join(outputPath, EXTRACT_GRAPH_FILENAME);
        const entitySummarizationPromptPath = path.join(outputPath, ENTITY_SUMMARIZATION_FILENAME);
        const communitySummarizationPromptPath = path.join(outputPath, COMMUNITY_SUMMARIZATION_FILENAME);
        
        // Write files to output path
        fs.writeFileSync(extractGraphPromptPath, prompts[0], 'utf-8');
        fs.writeFileSync(entitySummarizationPromptPath, prompts[1], 'utf-8');
        fs.writeFileSync(communitySummarizationPromptPath, prompts[2], 'utf-8');
        
        logger.info(`Prompts written to ${outputPath}`);
    } else {
        logger.error("No output path provided. Skipping writing prompts.");
    }
}