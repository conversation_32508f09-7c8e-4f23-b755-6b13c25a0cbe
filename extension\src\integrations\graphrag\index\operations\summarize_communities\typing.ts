/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'Finding' and 'CommunityReport' models.
 */

import { PipelineCache } from '../../../cache/pipeline-cache';
import { WorkflowCallbacks } from '../../../callbacks/workflow-callbacks';

export type ExtractedEntity = Record<string, any>;
export type StrategyConfig = Record<string, any>;
export type RowContext = Record<string, any>;
export type EntityTypes = string[];
export type Claim = Record<string, any>;

/**
 * Finding class definition.
 */
export interface Finding {
    summary: string;
    explanation: string;
}

/**
 * Community report class definition.
 */
export interface CommunityReport {
    community: string | number;
    title: string;
    summary: string;
    fullContent: string;
    fullContentJson: string;
    rank: number;
    level: number;
    ratingExplanation: string;
    findings: Finding[];
}

/**
 * Community reports strategy function type
 */
export type CommunityReportsStrategy = (
    communityId: string | number,
    communityContext: string,
    communityLevel: number,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    config: StrategyConfig
) => Promise<CommunityReport | null>;

/**
 * CreateCommunityReportsStrategyType enum definition.
 */
export enum CreateCommunityReportsStrategyType {
    graph_intelligence = "graph_intelligence"
}