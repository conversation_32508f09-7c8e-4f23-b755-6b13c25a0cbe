/**
 * @fileoverview Task Conflict Manager - Handles conflicts between sub-agents
 */

import { SubAgentState } from "../types"
import { SpawnAgentOptions } from "../tools/schema/agents/agent-spawner"

export interface TaskConflict {
	id: string
	type: "file_access" | "resource_lock" | "dependency" | "scope_overlap"
	agents: string[]
	resources: string[]
	severity: "low" | "medium" | "high" | "critical"
	description: string
	timestamp: number
	resolved: boolean
	resolution?: string
}

export interface TaskScope {
	agentId: string
	agentType: SpawnAgentOptions
	assignedFiles: string[]
	assignedDirectories: string[]
	taskDescription: string
	priority: number
	dependencies: string[]
	exclusiveResources: string[]
}

export class TaskConflictManager {
	private activeScopes: Map<string, TaskScope> = new Map()
	private detectedConflicts: Map<string, TaskConflict> = new Map()
	private resourceLocks: Map<string, string> = new Map() // resource -> agentId
	private conflictHistory: TaskConflict[] = []

	/**
	 * Register a new task scope for an agent
	 */
	public registerTaskScope(scope: TaskScope): void {
		this.activeScopes.set(scope.agentId, scope)
		this.detectConflicts(scope)
	}

	/**
	 * Remove task scope when agent completes or exits
	 */
	public unregisterTaskScope(agentId: string): void {
		const scope = this.activeScopes.get(agentId)
		if (scope) {
			// Release resource locks
			for (const resource of scope.exclusiveResources) {
				if (this.resourceLocks.get(resource) === agentId) {
					this.resourceLocks.delete(resource)
				}
			}
			this.activeScopes.delete(agentId)
		}
	}

	/**
	 * Detect conflicts between task scopes
	 */
	private detectConflicts(newScope: TaskScope): void {
		for (const [existingAgentId, existingScope] of this.activeScopes) {
			if (existingAgentId === newScope.agentId) {continue}

			const conflicts = this.analyzeConflicts(newScope, existingScope)
			for (const conflict of conflicts) {
				this.detectedConflicts.set(conflict.id, conflict)
				this.conflictHistory.push(conflict)
			}
		}
	}

	/**
	 * Analyze conflicts between two scopes
	 */
	private analyzeConflicts(scope1: TaskScope, scope2: TaskScope): TaskConflict[] {
		const conflicts: TaskConflict[] = []

		// File access conflicts
		const fileOverlap = this.findFileOverlap(scope1, scope2)
		if (fileOverlap.length > 0) {
			conflicts.push({
				id: `file_conflict_${Date.now()}_${Math.random()}`,
				type: "file_access",
				agents: [scope1.agentId, scope2.agentId],
				resources: fileOverlap,
				severity: this.calculateFileSeverity(scope1, scope2, fileOverlap),
				description: `File access conflict: ${fileOverlap.join(", ")}`,
				timestamp: Date.now(),
				resolved: false
			})
		}

		// Resource lock conflicts
		const resourceConflicts = this.findResourceConflicts(scope1, scope2)
		if (resourceConflicts.length > 0) {
			conflicts.push({
				id: `resource_conflict_${Date.now()}_${Math.random()}`,
				type: "resource_lock",
				agents: [scope1.agentId, scope2.agentId],
				resources: resourceConflicts,
				severity: "high",
				description: `Resource lock conflict: ${resourceConflicts.join(", ")}`,
				timestamp: Date.now(),
				resolved: false
			})
		}

		// Scope overlap conflicts
		if (this.hasScopeOverlap(scope1, scope2)) {
			conflicts.push({
				id: `scope_conflict_${Date.now()}_${Math.random()}`,
				type: "scope_overlap",
				agents: [scope1.agentId, scope2.agentId],
				resources: [],
				severity: "medium",
				description: `Task scope overlap between ${scope1.agentType} and ${scope2.agentType}`,
				timestamp: Date.now(),
				resolved: false
			})
		}

		return conflicts
	}

	/**
	 * Resolve conflicts using various strategies
	 */
	public resolveConflicts(): void {
		for (const [conflictId, conflict] of this.detectedConflicts) {
			if (conflict.resolved) {continue}

			const resolution = this.selectResolutionStrategy(conflict)
			this.applyResolution(conflict, resolution)
		}
	}

	/**
	 * Select appropriate resolution strategy
	 */
	private selectResolutionStrategy(conflict: TaskConflict): string {
		switch (conflict.type) {
			case "file_access":
				return this.resolveFileAccessConflict(conflict)
			case "resource_lock":
				return this.resolveResourceLockConflict(conflict)
			case "scope_overlap":
				return this.resolveScopeOverlapConflict(conflict)
			case "dependency":
				return this.resolveDependencyConflict(conflict)
			default:
				return "manual_intervention_required"
		}
	}

	/**
	 * Resolve file access conflicts
	 */
	private resolveFileAccessConflict(conflict: TaskConflict): string {
		const scopes = conflict.agents.map(id => this.activeScopes.get(id)).filter(Boolean)
		if (scopes.length !== 2) {return "insufficient_data"}

		const [scope1, scope2] = scopes

		// Priority-based resolution
		if (scope1!.priority > scope2!.priority) {
			return `priority_resolution:${scope1!.agentId}_takes_precedence`
		} else if (scope2!.priority > scope1!.priority) {
			return `priority_resolution:${scope2!.agentId}_takes_precedence`
		}

		// Agent type-based resolution
		const typeHierarchy = { "planner": 1, "analyzer": 2, "researcher": 3, "scholar": 4, "coder": 5, "sub_task": 6 }
		const type1Priority = typeHierarchy[scope1!.agentType] || 999
		const type2Priority = typeHierarchy[scope2!.agentType] || 999

		if (type1Priority < type2Priority) {
			return `type_resolution:${scope1!.agentId}_takes_precedence`
		} else {
			return `type_resolution:${scope2!.agentId}_takes_precedence`
		}
	}

	/**
	 * Resolve resource lock conflicts
	 */
	private resolveResourceLockConflict(conflict: TaskConflict): string {
		// First-come-first-served for resource locks
		const timestamps = conflict.agents.map(id => {
			const scope = this.activeScopes.get(id)
			return scope ? scope.priority : Date.now()
		})

		const earliestIndex = timestamps.indexOf(Math.min(...timestamps))
		const winnerAgent = conflict.agents[earliestIndex]
		
		return `resource_lock_resolution:${winnerAgent}_gets_exclusive_access`
	}

	/**
	 * Resolve scope overlap conflicts
	 */
	private resolveScopeOverlapConflict(conflict: TaskConflict): string {
		// Suggest task scope refinement
		return "scope_refinement_suggested"
	}

	/**
	 * Resolve dependency conflicts
	 */
	private resolveDependencyConflict(conflict: TaskConflict): string {
		// Implement dependency ordering
		return "dependency_ordering_applied"
	}

	/**
	 * Apply resolution to conflict
	 */
	private applyResolution(conflict: TaskConflict, resolution: string): void {
		conflict.resolved = true
		conflict.resolution = resolution
		
		// Log resolution
		console.log(`Conflict ${conflict.id} resolved: ${resolution}`)
		
		// Apply specific resolution actions
		if (resolution.startsWith("priority_resolution") || resolution.startsWith("type_resolution")) {
			const winnerAgent = resolution.split(":")[1].split("_takes_precedence")[0]
			this.grantResourceAccess(winnerAgent, conflict.resources)
		}
	}

	/**
	 * Grant resource access to specific agent
	 */
	private grantResourceAccess(agentId: string, resources: string[]): void {
		for (const resource of resources) {
			this.resourceLocks.set(resource, agentId)
		}
	}

	/**
	 * Check if agent can access resource
	 */
	public canAccessResource(agentId: string, resource: string): boolean {
		const lockHolder = this.resourceLocks.get(resource)
		return !lockHolder || lockHolder === agentId
	}

	/**
	 * Get active conflicts
	 */
	public getActiveConflicts(): TaskConflict[] {
		return Array.from(this.detectedConflicts.values()).filter(c => !c.resolved)
	}

	/**
	 * Get conflict history
	 */
	public getConflictHistory(): TaskConflict[] {
		return [...this.conflictHistory]
	}

	// Helper methods
	private findFileOverlap(scope1: TaskScope, scope2: TaskScope): string[] {
		const files1 = new Set([...scope1.assignedFiles, ...scope1.assignedDirectories])
		const files2 = new Set([...scope2.assignedFiles, ...scope2.assignedDirectories])
		
		return Array.from(files1).filter(file => files2.has(file))
	}

	private findResourceConflicts(scope1: TaskScope, scope2: TaskScope): string[] {
		const resources1 = new Set(scope1.exclusiveResources)
		const resources2 = new Set(scope2.exclusiveResources)
		
		return Array.from(resources1).filter(resource => resources2.has(resource))
	}

	private hasScopeOverlap(scope1: TaskScope, scope2: TaskScope): boolean {
		// Check if task descriptions suggest overlapping work
		const keywords1 = this.extractKeywords(scope1.taskDescription)
		const keywords2 = this.extractKeywords(scope2.taskDescription)
		
		const overlap = keywords1.filter(keyword => keywords2.includes(keyword))
		return overlap.length > 2 // Threshold for significant overlap
	}

	private extractKeywords(description: string): string[] {
		return description.toLowerCase()
			.split(/\s+/)
			.filter(word => word.length > 3)
			.slice(0, 10) // Top 10 keywords
	}

	private calculateFileSeverity(scope1: TaskScope, scope2: TaskScope, overlap: string[]): "low" | "medium" | "high" | "critical" {
		const totalFiles = scope1.assignedFiles.length + scope2.assignedFiles.length
		const overlapRatio = overlap.length / totalFiles

		if (overlapRatio > 0.7) {return "critical"}
		if (overlapRatio > 0.4) {return "high"}
		if (overlapRatio > 0.2) {return "medium"}
		return "low"
	}
}