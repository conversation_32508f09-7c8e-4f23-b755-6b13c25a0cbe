import * as fs from "fs/promises"
import * as path from "path"
import { minimatch } from "minimatch"

export interface FileMatchResult {
	path: string
	relativePath: string
	isDirectory: boolean
	matchedParts: string[]
}

export interface MatchOptions {
	recursive?: boolean
	caseSensitive?: boolean
	includeDirectories?: boolean
	maxResults?: number
}

/**
 * Advanced file matcher with wildcard and regex support
 * 
 * FEATURES:
 * - Glob pattern matching using minimatch library
 * - Regular expression matching with capture groups
 * - Recursive directory scanning with depth control
 * - Performance optimizations (visited set, max results)
 * - Pattern replacement for both glob and regex modes
 * 
 * WILDCARD PATTERNS:
 * - * : matches any characters except /
 * - ** : matches any characters including /
 * - ? : matches exactly one character
 * - [abc] : matches any character in the set
 * - [a-z] : matches any character in the range
 * 
 * REGEX PATTERNS:
 * - Standard JavaScript regex syntax
 * - Capture groups: (\w+), (\d+), (.+)
 * - Anchors: ^start, end$
 * - Character classes: \w, \d, \s
 * - Quantifiers: +, *, ?, {n,m}
 * 
 * PERFORMANCE LIMITS:
 * - Default max results: 1000 files
 * - Visited directory tracking to prevent cycles
 * - Early termination when limits reached
 * 
 * USAGE EXAMPLES:
 * - matchGlob("*.jsx", {recursive: true}) - find all JSX files
 * - matchRegex("component_(\w+)\.jsx$", {}) - find component files
 * - applyGlobReplacement(files, "*.jsx", "*.tsx") - rename extensions
 * - applyRegexReplacement(files, "(\w+)_test\.js", "$1.test.js") - standardize test names
 */
export class FileMatcher {
	constructor(private cwd: string) { }

	/**
	 * Match files using glob patterns
	 */
	async matchGlob(pattern: string, options: MatchOptions = {}): Promise<FileMatchResult[]> {
		const {
			recursive = false,
			caseSensitive = true,
			includeDirectories = false,
			maxResults = 1000
		} = options

		const results: FileMatchResult[] = []
		const visited = new Set<string>()

		// Normalize pattern
		const normalizedPattern = pattern.replace(/\\/g, '/')

		// Determine search depth
		const hasDoubleStar = normalizedPattern.includes('**')
		const searchRecursive = recursive || hasDoubleStar

		await this.scanDirectory(
			this.cwd,
			'',
			normalizedPattern,
			results,
			visited,
			{
				recursive: searchRecursive,
				caseSensitive,
				includeDirectories,
				maxResults
			}
		)

		return results.slice(0, maxResults)
	}

	/**
	 * Match files using regular expressions
	 */
	async matchRegex(regexPattern: string, options: MatchOptions = {}): Promise<FileMatchResult[]> {
		const {
			recursive = false,
			caseSensitive = true,
			includeDirectories = false,
			maxResults = 1000
		} = options

		const results: FileMatchResult[] = []
		const visited = new Set<string>()

		// Create regex with appropriate flags
		const flags = caseSensitive ? 'g' : 'gi'
		const regex = new RegExp(regexPattern, flags)

		await this.scanDirectoryRegex(
			this.cwd,
			'',
			regex,
			results,
			visited,
			{
				recursive,
				caseSensitive,
				includeDirectories,
				maxResults
			}
		)

		return results.slice(0, maxResults)
	}

	/**
	 * Apply replacement pattern to matched files
	 */
	applyGlobReplacement(matchedFiles: FileMatchResult[], pattern: string, replacement: string): Array<{
		source: string
		destination: string
		matchedParts: string[]
	}> {
		const results: Array<{
			source: string
			destination: string
			matchedParts: string[]
		}> = []

		for (const file of matchedFiles) {
			const destination = this.replaceGlobPattern(file.relativePath, pattern, replacement)
			if (destination && destination !== file.relativePath) {
				results.push({
					source: file.relativePath,
					destination,
					matchedParts: file.matchedParts
				})
			}
		}

		return results
	}

	/**
	 * Apply regex replacement to matched files
	 */
	applyRegexReplacement(matchedFiles: FileMatchResult[], regexPattern: string, replacement: string, caseSensitive: boolean = true): Array<{
		source: string
		destination: string
		matchedParts: string[]
	}> {
		const results: Array<{
			source: string
			destination: string
			matchedParts: string[]
		}> = []

		const flags = caseSensitive ? 'g' : 'gi'
		const regex = new RegExp(regexPattern, flags)

		for (const file of matchedFiles) {
			const fileName = path.basename(file.relativePath)
			const directory = path.dirname(file.relativePath)

			const newFileName = fileName.replace(regex, replacement)
			if (newFileName !== fileName) {
				const destination = directory === '.' ? newFileName : path.join(directory, newFileName)
				results.push({
					source: file.relativePath,
					destination,
					matchedParts: file.matchedParts
				})
			}
		}

		return results
	}

	private async scanDirectory(
		currentDir: string,
		relativePath: string,
		pattern: string,
		results: FileMatchResult[],
		visited: Set<string>,
		options: Required<MatchOptions>
	): Promise<void> {
		if (results.length >= options.maxResults) { return }

		const absolutePath = path.resolve(currentDir)
		if (visited.has(absolutePath)) { return }
		visited.add(absolutePath)

		try {
			const entries = await fs.readdir(currentDir, { withFileTypes: true })

			for (const entry of entries) {
				if (results.length >= options.maxResults) { break }

				const entryPath = path.join(currentDir, entry.name)
				const entryRelativePath = relativePath ? path.join(relativePath, entry.name) : entry.name
				const normalizedRelativePath = entryRelativePath.replace(/\\/g, '/')

				// Check if this entry matches the pattern
				const matchOptions = {
					nocase: !options.caseSensitive,
					dot: true
				}

				const isMatch = minimatch(normalizedRelativePath, pattern, matchOptions)

				if (isMatch && (options.includeDirectories || entry.isFile())) {
					// Extract matched parts for replacement
					const matchedParts = this.extractGlobMatches(normalizedRelativePath, pattern)

					results.push({
						path: entryPath,
						relativePath: entryRelativePath,
						isDirectory: entry.isDirectory(),
						matchedParts
					})
				}

				// Recurse into directories if needed
				if (entry.isDirectory() && options.recursive) {
					await this.scanDirectory(
						entryPath,
						entryRelativePath,
						pattern,
						results,
						visited,
						options
					)
				}
			}
		} catch (error) {
			// Skip directories we can't read
		}
	}

	private async scanDirectoryRegex(
		currentDir: string,
		relativePath: string,
		regex: RegExp,
		results: FileMatchResult[],
		visited: Set<string>,
		options: Required<MatchOptions>
	): Promise<void> {
		if (results.length >= options.maxResults) { return }

		const absolutePath = path.resolve(currentDir)
		if (visited.has(absolutePath)) { return }
		visited.add(absolutePath)

		try {
			const entries = await fs.readdir(currentDir, { withFileTypes: true })

			for (const entry of entries) {
				if (results.length >= options.maxResults) { break }

				const entryPath = path.join(currentDir, entry.name)
				const entryRelativePath = relativePath ? path.join(relativePath, entry.name) : entry.name

				// Test against filename only for regex
				const fileName = entry.name
				const matches = fileName.match(regex)

				if (matches && (options.includeDirectories || entry.isFile())) {
					results.push({
						path: entryPath,
						relativePath: entryRelativePath,
						isDirectory: entry.isDirectory(),
						matchedParts: matches.slice(1) // Capture groups only
					})
				}

				// Recurse into directories if needed
				if (entry.isDirectory() && options.recursive) {
					await this.scanDirectoryRegex(
						entryPath,
						entryRelativePath,
						regex,
						results,
						visited,
						options
					)
				}
			}
		} catch (error) {
			// Skip directories we can't read
		}
	}

	private extractGlobMatches(filePath: string, pattern: string): string[] {
		// Simple extraction of wildcard matches
		// This is a basic implementation - could be enhanced
		const matches: string[] = []

		// Split pattern and path by directory separators
		const patternParts = pattern.split('/')
		const pathParts = filePath.split('/')

		for (let i = 0; i < Math.min(patternParts.length, pathParts.length); i++) {
			const patternPart = patternParts[i]
			const pathPart = pathParts[i]

			if (patternPart.includes('*')) {
				// Extract the matched part
				const beforeStar = patternPart.split('*')[0]
				const afterStar = patternPart.split('*').slice(1).join('*')

				let matchedPart = pathPart
				if (beforeStar) {
					matchedPart = matchedPart.substring(beforeStar.length)
				}
				if (afterStar) {
					matchedPart = matchedPart.substring(0, matchedPart.length - afterStar.length)
				}

				matches.push(matchedPart)
			}
		}

		return matches
	}

	private replaceGlobPattern(filePath: string, pattern: string, replacement: string): string {
		// Simple glob replacement - replace * with matched parts
		let result = replacement
		const matches = this.extractGlobMatches(filePath, pattern)

		// Replace * in replacement pattern with matched parts
		let matchIndex = 0
		result = result.replace(/\*/g, () => {
			return matchIndex < matches.length ? matches[matchIndex++] : '*'
		})

		return result
	}
}