/**
 * Context Processor - 上下文分析与压缩
 * 
 * 核心功能：将复杂的开发上下文压缩成核心知识点
 * 灵感来源：人类记忆的压缩机制
 */

import { LearningContext, CompressionResult, Pattern, Knowledge } from '../types'
import { ContextCompressor } from './context-compressor'
import { SemanticAnalyzer } from './semantic-analyzer'

export class ContextProcessor {
	private compressor: ContextCompressor
	private analyzer: SemanticAnalyzer

	constructor() {
		this.compressor = new ContextCompressor()
		this.analyzer = new SemanticAnalyzer()
	}

	/**
	 * 处理学习上下文 - 主入口
	 */
	async processLearningContext(context: LearningContext): Promise<CompressionResult> {
		console.log(`[ContextProcessor] Processing context: ${context.id}`)

		// Step 1: 语义分析
		const semanticAnalysis = await this.analyzer.analyzeContext(context)
		
		// Step 2: 识别关键信息
		const keyInformation = this.extractKeyInformation(context, semanticAnalysis)
		
		// Step 3: 上下文压缩
		const compressionResult = await this.compressor.compressContext(
			context, 
			keyInformation
		)

		// Step 4: 质量评估
		const qualityScore = this.assessCompressionQuality(context, compressionResult)
		
		return {
			...compressionResult,
			confidence: qualityScore
		} as CompressionResult
	}

	/**
	 * 提取关键信息
	 */
	private extractKeyInformation(
		context: LearningContext, 
		semanticAnalysis: any
	): any {
		const keyInfo = {
			mainTopics: this.extractMainTopics(context.messages),
			actionSequences: this.extractActionSequences(context.messages),
			problemSolutions: this.extractProblemSolutions(context.messages),
			codePatterns: this.extractCodePatterns(context.messages),
			decisionPoints: this.extractDecisionPoints(context.messages)
		}

		return keyInfo
	}

	/**
	 * 提取主要话题
	 */
	private extractMainTopics(messages: any[]): string[] {
		const topics: string[] = []
		const topicKeywords = [
			'implement', 'create', 'fix', 'optimize', 'refactor', 
			'debug', 'test', 'deploy', 'configure', 'integrate'
		]

		for (const message of messages) {
			const content = this.extractTextFromMessage(message).toLowerCase()
			for (const keyword of topicKeywords) {
				if (content.includes(keyword)) {
					// Extract context around the keyword
					const sentences = content.split(/[.!?]+/)
					for (const sentence of sentences) {
						if (sentence.includes(keyword) && sentence.length > 20) {
							topics.push(sentence.trim())
							break
						}
					}
				}
			}
		}

		return [...new Set(topics)].slice(0, 5) // Top 5 unique topics
	}

	/**
	 * 提取行动序列
	 */
	private extractActionSequences(messages: any[]): string[] {
		const sequences: string[] = []
		const actionWords = ['first', 'then', 'next', 'finally', 'after', 'before']
		
		for (const message of messages) {
			const content = this.extractTextFromMessage(message)
			const sentences = content.split(/[.!?]+/)
			
			for (let i = 0; i < sentences.length - 1; i++) {
				const current = sentences[i].toLowerCase()
				const next = sentences[i + 1].toLowerCase()
				
				if (actionWords.some(word => current.includes(word) || next.includes(word))) {
					sequences.push(`${sentences[i].trim()} → ${sentences[i + 1].trim()}`)
				}
			}
		}

		return sequences.slice(0, 3) // Top 3 sequences
	}

	/**
	 * 提取问题解决方案
	 */
	private extractProblemSolutions(messages: any[]): Array<{problem: string, solution: string}> {
		const problemSolutions: Array<{problem: string, solution: string}> = []
		const problemIndicators = ['error', 'issue', 'problem', 'bug', 'fail', 'wrong']
		const solutionIndicators = ['fix', 'solve', 'resolve', 'correct', 'update', 'change']

		let currentProblem = ''
		
		for (const message of messages) {
			const content = this.extractTextFromMessage(message)
			const contentLower = content.toLowerCase()
			
			// Detect problem
			if (problemIndicators.some(indicator => contentLower.includes(indicator))) {
				const sentences = content.split(/[.!?]+/)
				for (const sentence of sentences) {
					if (problemIndicators.some(indicator => 
						sentence.toLowerCase().includes(indicator)
					)) {
						currentProblem = sentence.trim()
						break
					}
				}
			}
			
			// Detect solution
			if (currentProblem && solutionIndicators.some(indicator => 
				contentLower.includes(indicator)
			)) {
				const sentences = content.split(/[.!?]+/)
				for (const sentence of sentences) {
					if (solutionIndicators.some(indicator => 
						sentence.toLowerCase().includes(indicator)
					)) {
						problemSolutions.push({
							problem: currentProblem,
							solution: sentence.trim()
						})
						currentProblem = '' // Reset
						break
					}
				}
			}
		}

		return problemSolutions.slice(0, 3) // Top 3 problem-solution pairs
	}

	/**
	 * 提取代码模式
	 */
	private extractCodePatterns(messages: any[]): string[] {
		const patterns: string[] = []
		
		for (const message of messages) {
			const content = this.extractTextFromMessage(message)
			
			// Look for code blocks
			const codeBlocks = content.match(/```[\s\S]*?```/g) || []
			for (const block of codeBlocks) {
				const code = block.replace(/```\w*\n?/g, '').trim()
				if (code.length > 50 && code.length < 500) {
					// Extract pattern description
					const lines = code.split('\n')
					const firstLine = lines[0]
					if (firstLine.includes('function') || firstLine.includes('class') || 
						firstLine.includes('const') || firstLine.includes('interface')) {
						patterns.push(`Code pattern: ${firstLine}`)
					}
				}
			}
		}

		return patterns.slice(0, 3) // Top 3 code patterns
	}

	/**
	 * 提取决策点
	 */
	private extractDecisionPoints(messages: any[]): string[] {
		const decisions: string[] = []
		const decisionWords = ['choose', 'decide', 'select', 'prefer', 'recommend', 'suggest']
		
		for (const message of messages) {
			const content = this.extractTextFromMessage(message)
			const sentences = content.split(/[.!?]+/)
			
			for (const sentence of sentences) {
				const sentenceLower = sentence.toLowerCase()
				if (decisionWords.some(word => sentenceLower.includes(word))) {
					decisions.push(sentence.trim())
				}
			}
		}

		return decisions.slice(0, 3) // Top 3 decisions
	}

	/**
	 * 评估压缩质量
	 */
	private assessCompressionQuality(
		original: LearningContext, 
		compressed: Omit<CompressionResult, 'confidence'>
	): number {
		let score = 0.5 // Base score

		// Information retention score
		const originalLength = original.originalContext.length
		const compressedLength = compressed.coreInsights.join(' ').length
		const compressionRatio = compressedLength / originalLength
		
		// Optimal compression ratio is around 0.1 (10:1)
		if (compressionRatio >= 0.05 && compressionRatio <= 0.2) {
			score += 0.2
		}

		// Pattern quality score
		if (compressed.keyPatterns.length > 0) {
			score += 0.15
		}

		// Knowledge actionability score
		if (compressed.actionableKnowledge.length > 0) {
			score += 0.15
		}

		return Math.min(score, 1.0)
	}

	/**
	 * 从消息中提取文本内容
	 */
	private extractTextFromMessage(message: any): string {
		if (!message?.content) {
			return ""
		}
		
		if (typeof message.content === "string") {
			return message.content
		}
		
		if (Array.isArray(message.content)) {
			return message.content
				.filter((block: any) => block.type === "text")
				.map((block: any) => block.text)
				.join(" ")
		}
		
		return ""
	}
}