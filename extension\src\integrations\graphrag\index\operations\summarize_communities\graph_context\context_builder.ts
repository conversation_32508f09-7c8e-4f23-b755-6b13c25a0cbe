// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Context builders for graphs.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';
import { WorkflowCallbacks } from '../../../../callbacks/workflow-callbacks.js';
import { buildMixedContext } from '../build-mixed-context.js';
import { parallelSortContextBatch, sortContext } from './sort-context.js';
import { getLevels } from '../utils.js';
import {
  antijoin,
  dropColumns,
  join,
  select,
  transformSeries,
  union,
  whereColumnEquals,
} from '../../../utils/dataframes.js';
import { progressIterable } from '../../../../logger/progress.js';
import { numTokens } from '../../../../query/llm/text-utils.js';

/**
 * Prep communities for report generation.
 */
export function buildLocalContext(
  nodes: DataFrame,
  edges: DataFrame,
  claims: DataFrame,
  callbacks: WorkflowCallbacks,
  maxContextTokens: number = 16000
): DataFrame {
  const levels = getLevels(nodes, schemas.COMMUNITY_LEVEL);
  const dfs: DataFrame[] = [];

  for (const level of progressIterable(levels, callbacks.progress, levels.length)) {
    const communitiesAtLevelDf = prepareReportsAtLevel(
      nodes,
      edges,
      claims,
      level,
      maxContextTokens
    );

    communitiesAtLevelDf.setColumn(schemas.COMMUNITY_LEVEL, level);
    dfs.push(communitiesAtLevelDf);
  }

  // Build initial local context for all communities
  return DataFrame.concat(dfs);
}

/**
 * Prepare reports at a given level.
 */
function prepareReportsAtLevel(
  nodeDf: DataFrame,
  edgeDf: DataFrame,
  claimDf: DataFrame | undefined,
  level: number,
  maxContextTokens: number = 16000
): DataFrame {
  // Filter and prepare node details
  const levelNodeDf = nodeDf.filter(row => row[schemas.COMMUNITY_LEVEL] === level);
  console.log(`Number of nodes at level=${level} => ${levelNodeDf.length}`);
  const nodesSet = new Set(levelNodeDf.getColumn(schemas.TITLE));

  // Filter and prepare edge details
  let levelEdgeDf = edgeDf.filter(row =>
    nodesSet.has(row[schemas.EDGE_SOURCE]) && nodesSet.has(row[schemas.EDGE_TARGET])
  );

  levelEdgeDf = levelEdgeDf.withColumn(
    schemas.EDGE_DETAILS,
    levelEdgeDf.select([
      schemas.SHORT_ID,
      schemas.EDGE_SOURCE,
      schemas.EDGE_TARGET,
      schemas.DESCRIPTION,
      schemas.EDGE_DEGREE,
    ]).toRecords()
  );

  let levelClaimDf = DataFrame.empty();
  if (claimDf) {
    levelClaimDf = claimDf.filter(row =>
      nodesSet.has(row[schemas.CLAIM_SUBJECT])
    );
  }

  // Merge node and edge details
  // Group edge details by node and aggregate into lists
  const sourceEdges = levelEdgeDf
    .groupBy(schemas.EDGE_SOURCE)
    .agg({ [schemas.EDGE_DETAILS]: 'first' })
    .rename({ [schemas.EDGE_SOURCE]: schemas.TITLE });

  const targetEdges = levelEdgeDf
    .groupBy(schemas.EDGE_TARGET)
    .agg({ [schemas.EDGE_DETAILS]: 'first' })
    .rename({ [schemas.EDGE_TARGET]: schemas.TITLE });

  // Merge aggregated edges into the node DataFrame
  let mergedNodeDf = levelNodeDf
    .merge(sourceEdges, schemas.TITLE, 'left')
    .merge(targetEdges, schemas.TITLE, 'left');

  // Combine source and target edge details into a single column
  mergedNodeDf = mergedNodeDf.withColumn(
    schemas.EDGE_DETAILS,
    mergedNodeDf.getColumn(`${schemas.EDGE_DETAILS}_x`)
      .combineFirst(mergedNodeDf.getColumn(`${schemas.EDGE_DETAILS}_y`))
  );

  // Drop intermediate columns
  mergedNodeDf = mergedNodeDf.drop([`${schemas.EDGE_DETAILS}_x`, `${schemas.EDGE_DETAILS}_y`]);

  // Aggregate node and edge details
  mergedNodeDf = mergedNodeDf
    .groupBy([
      schemas.TITLE,
      schemas.COMMUNITY_ID,
      schemas.COMMUNITY_LEVEL,
      schemas.NODE_DEGREE,
    ])
    .agg({
      [schemas.NODE_DETAILS]: 'first',
      [schemas.EDGE_DETAILS]: (x: any[]) => x.filter(item => item != null),
    });

  // Merge claim details if available
  if (claimDf) {
    mergedNodeDf = mergedNodeDf.merge(
      levelClaimDf
        .select([schemas.CLAIM_SUBJECT, schemas.CLAIM_DETAILS])
        .rename({ [schemas.CLAIM_SUBJECT]: schemas.TITLE }),
      schemas.TITLE,
      'left'
    );
  }

  // Create the ALL_CONTEXT column
  const contextColumns = [
    schemas.TITLE,
    schemas.NODE_DEGREE,
    schemas.NODE_DETAILS,
    schemas.EDGE_DETAILS,
  ];
  
  if (claimDf) {
    contextColumns.push(schemas.CLAIM_DETAILS);
  }

  mergedNodeDf = mergedNodeDf.withColumn(
    schemas.ALL_CONTEXT,
    mergedNodeDf.select(contextColumns).toRecords()
  );

  // Group all node details by community
  const communityDf = mergedNodeDf
    .groupBy(schemas.COMMUNITY_ID)
    .agg({ [schemas.ALL_CONTEXT]: 'collect_list' });

  // Generate community-level context strings using vectorized batch processing
  return parallelSortContextBatch(communityDf, maxContextTokens);
}

/**
 * Prep context for each community in a given level.
 */
export function buildLevelContext(
  reportDf: DataFrame | undefined,
  communityHierarchyDf: DataFrame,
  localContextDf: DataFrame,
  level: number,
  maxContextTokens: number
): DataFrame {
  // Filter by community level
  const levelContextDf = localContextDf.filter(
    row => row[schemas.COMMUNITY_LEVEL] === level
  );

  // Filter valid and invalid contexts using boolean logic
  const validContextDf = levelContextDf.filter(
    row => !row[schemas.CONTEXT_EXCEED_FLAG]
  );
  const invalidContextDf = levelContextDf.filter(
    row => row[schemas.CONTEXT_EXCEED_FLAG]
  );

  // There is no report to substitute with, so we just trim the local context
  if (invalidContextDf.isEmpty()) {
    return validContextDf;
  }

  if (!reportDf || reportDf.isEmpty()) {
    const trimmedInvalidDf = invalidContextDf.withColumn(
      schemas.CONTEXT_STRING,
      sortAndTrimContext(invalidContextDf, maxContextTokens)
    );

    const updatedInvalidDf = trimmedInvalidDf
      .withColumn(
        schemas.CONTEXT_SIZE,
        trimmedInvalidDf.getColumn(schemas.CONTEXT_STRING).map(numTokens)
      )
      .withColumn(schemas.CONTEXT_EXCEED_FLAG, false);

    return union(validContextDf, updatedInvalidDf);
  }

  const levelContextDfFiltered = antijoinReports(levelContextDf, reportDf);

  // For each invalid context, try to substitute with sub-community reports
  const subContextDf = getSubcontextDf(level + 1, reportDf, localContextDf);
  const communityDf = getCommunityDf(
    level,
    invalidContextDf,
    subContextDf,
    communityHierarchyDf,
    maxContextTokens
  );

  // Handle any remaining invalid records
  const remainingDf = antijoinReports(invalidContextDf, communityDf)
    .withColumn(
      schemas.CONTEXT_STRING,
      sortAndTrimContext(invalidContextDf, maxContextTokens)
    );

  const result = union(validContextDf, communityDf, remainingDf)
    .withColumn(
      schemas.CONTEXT_SIZE,
      (df: DataFrame) => df.getColumn(schemas.CONTEXT_STRING).map(numTokens)
    )
    .withColumn(schemas.CONTEXT_EXCEED_FLAG, false);

  return result;
}

// Helper functions
function dropCommunityLevel(df: DataFrame): DataFrame {
  return dropColumns(df, schemas.COMMUNITY_LEVEL);
}

function atLevel(level: number, df: DataFrame): DataFrame {
  return whereColumnEquals(df, schemas.COMMUNITY_LEVEL, level);
}

function antijoinReports(df: DataFrame, reports: DataFrame): DataFrame {
  return antijoin(df, reports, schemas.COMMUNITY_ID);
}

function sortAndTrimContext(df: DataFrame, maxContextTokens: number): any[] {
  const series = df.getColumn(schemas.ALL_CONTEXT);
  return transformSeries(
    series,
    (x: any) => sortContext(x, maxContextTokens)
  );
}

function buildMixedContextSeries(df: DataFrame, maxContextTokens: number): any[] {
  const series = df.getColumn(schemas.ALL_CONTEXT);
  return transformSeries(
    series,
    (x: any) => buildMixedContext(x, maxContextTokens)
  );
}

function getSubcontextDf(
  level: number,
  reportDf: DataFrame,
  localContextDf: DataFrame
): DataFrame {
  const subReportDf = dropCommunityLevel(atLevel(level, reportDf));
  let subContextDf = atLevel(level, localContextDf);
  subContextDf = join(subContextDf, subReportDf, schemas.COMMUNITY_ID);
  return subContextDf.rename({ [schemas.COMMUNITY_ID]: schemas.SUB_COMMUNITY });
}

function getCommunityDf(
  level: number,
  invalidContextDf: DataFrame,
  subContextDf: DataFrame,
  communityHierarchyDf: DataFrame,
  maxContextTokens: number
): DataFrame {
  // Collect all sub communities' contexts for each community
  const communityDf = dropCommunityLevel(atLevel(level, communityHierarchyDf));
  const invalidCommunityIds = select(invalidContextDf, schemas.COMMUNITY_ID);
  const subcontextSelection = select(
    subContextDf,
    schemas.SUB_COMMUNITY,
    schemas.FULL_CONTENT,
    schemas.ALL_CONTEXT,
    schemas.CONTEXT_SIZE
  );

  const invalidCommunities = join(
    communityDf,
    invalidCommunityIds,
    schemas.COMMUNITY_ID,
    'inner'
  );

  let resultDf = join(invalidCommunities, subcontextSelection, schemas.SUB_COMMUNITY);

  resultDf = resultDf.withColumn(
    schemas.ALL_CONTEXT,
    resultDf.toRecords().map(row => ({
      [schemas.SUB_COMMUNITY]: row[schemas.SUB_COMMUNITY],
      [schemas.ALL_CONTEXT]: row[schemas.ALL_CONTEXT],
      [schemas.FULL_CONTENT]: row[schemas.FULL_CONTENT],
      [schemas.CONTEXT_SIZE]: row[schemas.CONTEXT_SIZE],
    }))
  );

  resultDf = resultDf
    .groupBy(schemas.COMMUNITY_ID)
    .agg({ [schemas.ALL_CONTEXT]: 'collect_list' });

  return resultDf
    .withColumn(
      schemas.CONTEXT_STRING,
      buildMixedContextSeries(resultDf, maxContextTokens)
    )
    .withColumn(schemas.COMMUNITY_LEVEL, level);
}
