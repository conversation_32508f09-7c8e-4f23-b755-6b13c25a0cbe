/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the summarize_descriptions verb.
 */

import { DataFrame } from '../../../data-model/types';
import { PipelineCache } from '../../../cache/pipeline-cache';
import { WorkflowCallbacks } from '../../../callbacks/workflow-callbacks';
import { 
    SummarizationStrategy, 
    SummarizeStrategyType,
    SummarizedDescriptionResult 
} from './typing';

const logger = console;

/**
 * Summarize entity and relationship descriptions from an entity graph, using a language model.
 */
export async function summarizeDescriptions(
    entitiesDF: DataFrame,
    relationshipsDF: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    strategy?: Record<string, any>,
    numThreads: number = 4
): Promise<[DataFrame, DataFrame]> {
    logger.debug("summarize_descriptions strategy=", strategy);
    
    const strategyConfig = { ...strategy };
    const strategyExec = loadStrategy(
        strategy?.type || SummarizeStrategyType.graph_intelligence
    );

    async function getSummarized(
        nodes: DataFrame, 
        edges: DataFrame, 
        semaphore: Semaphore
    ): Promise<[DataFrame, DataFrame]> {
        const tickerLength = nodes.data.length + edges.data.length;
        let completed = 0;
        
        const ticker = (increment: number) => {
            completed += increment;
            if (callbacks.progress) {
                callbacks.progress(
                    completed / tickerLength, 
                    "Summarize entity/relationship description progress: "
                );
            }
        };

        // Process nodes (entities)
        const nodeFutures = nodes.data.map(row => 
            doSummarizeDescriptions(
                row.title,
                Array.from(new Set(row.description)).sort(),
                ticker,
                semaphore
            )
        );

        const nodeResults = await Promise.all(nodeFutures);

        const nodeDescriptions = nodeResults.map(result => ({
            title: result.id,
            description: result.description
        }));

        // Process edges (relationships)
        const edgeFutures = edges.data.map(row =>
            doSummarizeDescriptions(
                [row.source, row.target],
                Array.from(new Set(row.description)).sort(),
                ticker,
                semaphore
            )
        );

        const edgeResults = await Promise.all(edgeFutures);

        const edgeDescriptions = edgeResults.map(result => ({
            source: (result.id as [string, string])[0],
            target: (result.id as [string, string])[1],
            description: result.description
        }));

        const entityDescriptions: DataFrame = {
            columns: ['title', 'description'],
            data: nodeDescriptions
        };

        const relationshipDescriptions: DataFrame = {
            columns: ['source', 'target', 'description'],
            data: edgeDescriptions
        };

        return [entityDescriptions, relationshipDescriptions];
    }

    async function doSummarizeDescriptions(
        id: string | [string, string],
        descriptions: string[],
        ticker: (increment: number) => void,
        semaphore: Semaphore
    ): Promise<SummarizedDescriptionResult> {
        await semaphore.acquire();
        try {
            const results = await strategyExec(id, descriptions, cache, strategyConfig);
            ticker(1);
            return results;
        } finally {
            semaphore.release();
        }
    }

    const semaphore = new Semaphore(numThreads);

    return await getSummarized(entitiesDF, relationshipsDF, semaphore);
}

/**
 * Load strategy method definition.
 */
function loadStrategy(strategyType: SummarizeStrategyType): SummarizationStrategy {
    switch (strategyType) {
        case SummarizeStrategyType.graph_intelligence:
            return async (id, descriptions, cache, config) => {
                // Simplified implementation - in reality you'd use LLM
                console.warn('Graph intelligence summarization strategy not fully implemented');
                
                const combinedDescription = descriptions.join('. ');
                const summary = combinedDescription.length > 200 
                    ? combinedDescription.substring(0, 200) + '...'
                    : combinedDescription;
                
                return {
                    id,
                    description: summary || 'No description available'
                };
            };
        default:
            throw new Error(`Unknown strategy: ${strategyType}`);
    }
}

/**
 * Simple semaphore implementation for controlling concurrency.
 */
class Semaphore {
    private permits: number;
    private waitQueue: Array<() => void> = [];

    constructor(permits: number) {
        this.permits = permits;
    }

    async acquire(): Promise<void> {
        if (this.permits > 0) {
            this.permits--;
            return;
        }

        return new Promise<void>((resolve) => {
            this.waitQueue.push(resolve);
        });
    }

    release(): void {
        if (this.waitQueue.length > 0) {
            const resolve = this.waitQueue.shift()!;
            resolve();
        } else {
            this.permits++;
        }
    }
}