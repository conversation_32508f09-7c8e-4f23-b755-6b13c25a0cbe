# 工具开发技能模板

## 技能基本信息
- **技能名称**: add_new_agent_tool
- **技能类别**: 工具开发
- **适用场景**: 扩展AI助手功能，添加新工具
- **成功率目标**: 100%

## 详细工作流程

### 第1步: 创建工具模式定义
- **工具**: fsWrite
- **目标**: 创建完整的工具定义文件
- **文件路径**: `extension/src/agent/v1/tools/schema/{toolName}.ts`
- **模板结构**:
  ```typescript
  import { z } from "zod"
  
  /**
   * @tool {toolName}
   * @description {工具描述}
   * @schema {zod模式定义}
   * @example {使用示例}
   */
  const schema = z.object({
    // 参数定义
  })
  
  const examples = [
    // 示例数组
  ]
  
  export const {toolName}Tool = {
    schema: { name: "{toolName}", schema },
    examples,
  }
  ```
- **预期结果**: 完整的工具模式定义文件

### 第2步: 注册工具到索引
- **工具**: strReplace
- **目标**: 在index.ts中注册新工具
- **文件路径**: `extension/src/agent/v1/tools/schema/index.ts`
- **操作**:
  ```typescript
  // 添加导入
  import { {toolName}Tool } from "./{toolName}"
  
  // 添加到导出
  export const tools = [
    // 现有工具...
    {toolName}Tool,
  ]
  ```
- **预期结果**: 工具成功注册到工具系统

### 第3步: 添加TypeScript类型定义
- **工具**: strReplace
- **目标**: 在类型定义文件中添加新工具类型
- **文件路径**: `extension/src/shared/new-tools.ts`
- **操作**:
  ```typescript
  // 添加工具类型
  export interface {ToolName}Tool {
    name: "{toolName}";
    // 参数类型定义
  }
  
  // 更新联合类型
  export type ChatTool = 
    | ExistingTool1
    | ExistingTool2
    | {ToolName}Tool;
  ```
- **预期结果**: 完整的TypeScript类型支持

### 第4步: 创建UI组件
- **工具**: fsWrite
- **目标**: 创建工具的UI展示组件
- **文件路径**: `extension/src/components/chat/tools/{ToolName}Tool.tsx`
- **模板结构**:
  ```tsx
  import React from 'react';
  import { {ToolName}Tool } from '../../../shared/new-tools';
  
  interface Props {
    tool: {ToolName}Tool;
  }
  
  export const {ToolName}ToolComponent: React.FC<Props> = ({ tool }) => {
    return (
      <div className="tool-component">
        {/* UI实现 */}
      </div>
    );
  };
  ```
- **预期结果**: 功能完整的UI组件

### 第5步: 注册UI组件
- **工具**: strReplace
- **目标**: 在工具渲染器中注册新组件
- **文件路径**: `extension/src/components/chat/tools/ToolRenderer.tsx`
- **操作**:
  ```tsx
  import { {ToolName}ToolComponent } from './{ToolName}Tool';
  
  // 在渲染逻辑中添加
  case '{toolName}':
    return <{ToolName}ToolComponent tool={tool} />;
  ```
- **预期结果**: UI组件成功集成

### 第6步: 构建和测试
- **工具**: execute_command
- **目标**: 验证工具正常工作
- **命令**: `npm run build`
- **验证项目**:
  - TypeScript编译通过
  - 工具在UI中正确显示
  - 所有参数正确传递
- **预期结果**: 工具完全可用

## 常见陷阱和解决方案

### 陷阱1: 忘记更新ChatTool联合类型
- **问题**: TypeScript类型检查失败
- **解决方案**: 立即更新联合类型定义
- **预防措施**: 建立检查清单

### 陷阱2: UI组件缺少参数支持
- **问题**: 工具参数无法在UI中显示
- **解决方案**: 确保组件props包含所有工具参数
- **预防措施**: 对照模式检查组件参数

### 陷阱3: 示例格式不正确
- **问题**: 工具示例无法正确解析
- **解决方案**: 使用标准XML格式
- **预防措施**: 参考现有工具示例

## 成功指标

### 主要指标
- 构建成功，工具正常工作
- TypeScript类型检查通过
- UI组件正确渲染

### 次要指标
- 所有参数正确显示
- 示例可以正常执行
- 代码风格一致

## 触发关键词
- "添加新工具"
- "创建代理工具"
- "扩展工具功能"
- "工具开发"

## 应用约束
- 工具名称必须遵循命名规范
- 模式必须包含完整的zod验证
- UI组件必须遵循现有设计模式
- 必须提供完整的使用示例

## 相关技能
- typescript_development
- react_component_creation
- ui_integration
- testing_workflow

## 使用示例

### 示例1: 创建文件搜索工具
```
需求: 添加高级文件搜索功能
工具序列: fsWrite -> strReplace -> strReplace -> fsWrite -> strReplace -> execute_command
结果: 成功创建并集成文件搜索工具
```

### 示例2: 创建代码分析工具
```
需求: 添加代码质量分析功能
工具序列: fsWrite -> strReplace -> strReplace -> fsWrite -> strReplace -> execute_command
结果: 代码分析工具完全可用
```

## 技能进化记录

### 版本1.0
- 基础工具创建流程
- 成功率: 85%

### 版本2.0
- 增加UI组件创建
- 成功率: 95%

### 版本3.0
- 完善类型定义流程
- 成功率: 100%

## 学习调用示例

```xml
<learning>
  <action>create_skill</action>
  <skillName>add_new_agent_tool</skillName>
  <skillData>{
    "description": "标准化的代理工具添加流程，包括模式定义、类型声明、UI组件和系统集成",
    "workflow": ["create_tool_schema", "update_schema_index", "add_type_definition", "create_ui_component", "register_ui_component", "build_and_test"],
    "toolCombinations": ["fsWrite -> strReplace -> strReplace -> fsWrite -> strReplace -> execute_command"],
    "successRate": 100
  }</skillData>
</learning>
```