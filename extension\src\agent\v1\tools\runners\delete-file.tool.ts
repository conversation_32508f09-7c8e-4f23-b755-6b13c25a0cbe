import * as fs from "fs/promises"
import * as path from "path"
import { serializeError } from "serialize-error"
import { BaseAgentTool } from "../base-agent.tool"
import { DeleteFileToolParams } from "../schema/delete_file"
import { getReadablePath } from "../../utils"

export class DeleteFileTool extends BaseAgentTool<DeleteFileToolParams> {

	/**
	 * Delete a single file or directory
	 */
	private async deleteSingle(filePath: string, force: boolean = false): Promise<{
		success: boolean
		isDirectory: boolean
		error?: string
	}> {
		try {
			const resolvedPath = path.resolve(this.cwd, filePath)
			const stats = await fs.stat(resolvedPath)
			const isDirectory = stats.isDirectory()

			if (isDirectory) {
				if (force) {
					await fs.rm(resolvedPath, { recursive: true, force: true })
				} else {
					// Try to remove empty directory
					await fs.rmdir(resolvedPath)
				}
			} else {
				await fs.unlink(resolvedPath)
			}

			return {
				success: true,
				isDirectory
			}
		} catch (error) {
			return {
				success: false,
				isDirectory: false,
				error: error instanceof Error ? error.message : String(error)
			}
		}
	}

	async execute() {
		const { input } = this.params

		// Single file mode only
		if (!input.path) {
			return this.toolResponse("error", "Path is required for single file deletion")
		}

		const isDryRun = input.dry_run || false
		const force = input.force || false

		// Validate path
		if (typeof input.path !== 'string' || input.path.trim() === '') {
			return this.toolResponse("error", "Invalid path provided")
		}

		const filePath = input.path.trim()

		// Check if file exists
		try {
			const resolvedPath = path.resolve(this.cwd, filePath)
			const stats = await fs.stat(resolvedPath)
			const isDirectory = stats.isDirectory()

			if (isDryRun) {
				// Preview mode
				const result = `
				<delete_file_response>
					<status>
						<r>success</r>
						<operation>delete_file</operation>
						<mode>preview</mode>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<preview_details>
						<target_path>${getReadablePath(filePath, this.cwd)}</target_path>
						<target_type>${isDirectory ? 'directory' : 'file'}</target_type>
						<force_delete>${force}</force_delete>
						<would_delete>true</would_delete>
						<message>Preview: ${isDirectory ? 'Directory' : 'File'} would be deleted${force && isDirectory ? ' (recursive)' : ''}</message>
					</preview_details>
				</delete_file_response>`
				return this.toolResponse("success", result)
			}

			// Actual deletion
			const deleteResult = await this.deleteSingle(filePath, force)

			if (deleteResult.success) {
				const result = `
				<delete_file_response>
					<status>
						<r>success</r>
						<operation>delete_file</operation>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<operation_details>
						<deleted_path>${getReadablePath(filePath, this.cwd)}</deleted_path>
						<target_type>${deleteResult.isDirectory ? 'directory' : 'file'}</target_type>
						<force_delete>${force}</force_delete>
						<message>${deleteResult.isDirectory ? 'Directory' : 'File'} deleted successfully</message>
					</operation_details>
				</delete_file_response>`
				return this.toolResponse("success", result)
			} else {
				const result = `
				<delete_file_response>
					<status>
						<r>error</r>
						<operation>delete_file</operation>
						<timestamp>${new Date().toISOString()}</timestamp>
					</status>
					<error_details>
						<type>deletion_failed</type>
						<target_path>${getReadablePath(filePath, this.cwd)}</target_path>
						<message>Failed to delete: ${deleteResult.error}</message>
					</error_details>
				</delete_file_response>`
				return this.toolResponse("error", result)
			}

		} catch (error) {
			// File doesn't exist or other error
			const result = `
			<delete_file_response>
				<status>
					<r>error</r>
					<operation>delete_file</operation>
					<timestamp>${new Date().toISOString()}</timestamp>
				</status>
				<error_details>
					<type>file_not_found</type>
					<target_path>${getReadablePath(filePath, this.cwd)}</target_path>
					<message>File or directory not found: ${error instanceof Error ? error.message : String(error)}</message>
				</error_details>
			</delete_file_response>`
			return this.toolResponse("error", result)
		}
	}
}