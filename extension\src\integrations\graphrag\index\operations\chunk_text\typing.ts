/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'TextChunk' model.
 */

import { ChunkingConfig } from '../../../config/models/chunking-config';

/**
 * Text chunk class definition.
 */
export interface TextChunk {
    /** The text chunk content */
    textChunk: string;
    /** Source document indices */
    sourceDocIndices: number[];
    /** Number of tokens (optional) */
    nTokens?: number;
}

/**
 * Input to a chunking strategy. Can be a string, a list of strings, or a list of tuples of (id, text).
 */
export type ChunkInput = string | string[] | Array<[string, string]>;

/**
 * Chunk strategy function type
 */
export type ChunkStrategy = (
    input: string[],
    config: ChunkingConfig,
    tick: (increment: number) => void
) => Iterable<TextChunk>;