/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Graph extraction using NLP.
 */

import { DataFrame } from '../../../data-model/types';
import { PipelineCache } from '../../../cache/pipeline-cache';
import { NoopPipelineCache } from '../../../cache/noop-pipeline-cache';
import { AsyncType } from '../../../config/enums';
import { deriveFromRows } from '../../utils/derive-from-rows';
import { calculatePmiEdgeWeights } from '../../utils/graphs';
import { genSha512Hash } from '../../utils/hashing';

/**
 * Base noun phrase extractor interface
 */
export interface BaseNounPhraseExtractor {
    extract(text: string): string[];
    toString(): string;
}

/**
 * Build a noun graph from text units.
 * @param textUnitDF - DataFrame with text units
 * @param textAnalyzer - Noun phrase extractor
 * @param normalizeEdgeWeights - Whether to normalize edge weights using PMI
 * @param numThreads - Number of threads for parallel processing
 * @param cache - Pipeline cache
 * @returns Tuple of [nodes DataFrame, edges DataFrame]
 */
export async function buildNounGraph(
    textUnitDF: DataFrame,
    textAnalyzer: BaseNounPhraseExtractor,
    normalizeEdgeWeights: boolean,
    numThreads: number = 4,
    cache?: PipelineCache
): Promise<[DataFrame, DataFrame]> {
    // Select relevant columns
    const textUnits: DataFrame = {
        columns: ['id', 'text'],
        data: textUnitDF.data.map(row => ({
            id: row.id,
            text: row.text
        }))
    };

    const nodesDF = await extractNodes(
        textUnits,
        textAnalyzer,
        numThreads,
        cache
    );
    
    const edgesDF = extractEdges(nodesDF, normalizeEdgeWeights);
    
    return [nodesDF, edgesDF];
}

/**
 * Extract initial nodes and edges from text units.
 * Input: text unit df with schema [id, text, document_id]
 * Returns a dataframe with schema [id, title, frequency, text_unit_ids].
 */
async function extractNodes(
    textUnitDF: DataFrame,
    textAnalyzer: BaseNounPhraseExtractor,
    numThreads: number = 4,
    cache?: PipelineCache
): Promise<DataFrame> {
    const cacheInstance = cache || new NoopPipelineCache();
    const childCache = cacheInstance.child("extract_noun_phrases");

    async function extract(row: Record<string, any>): Promise<string[]> {
        const text = row.text;
        const attrs = { text: text, analyzer: textAnalyzer.toString() };
        const key = genSha512Hash(attrs, Object.keys(attrs));
        
        let result = await childCache.get(key);
        if (!result) {
            result = textAnalyzer.extract(text);
            await childCache.set(key, result);
        }
        return result;
    }

    const nounPhrases = await deriveFromRows(
        textUnitDF,
        extract,
        undefined,
        numThreads,
        AsyncType.Threaded,
        "extract noun phrases progress: "
    );

    // Create exploded dataframe
    const explodedData: Array<{ title: string; text_unit_id: string }> = [];
    textUnitDF.data.forEach((row, index) => {
        const phrases = nounPhrases[index];
        if (phrases && Array.isArray(phrases)) {
            phrases.forEach(phrase => {
                explodedData.push({
                    title: phrase,
                    text_unit_id: row.id
                });
            });
        }
    });

    // Group by title and aggregate text_unit_ids
    const groupedMap = new Map<string, string[]>();
    explodedData.forEach(item => {
        if (!groupedMap.has(item.title)) {
            groupedMap.set(item.title, []);
        }
        groupedMap.get(item.title)!.push(item.text_unit_id);
    });

    // Create final nodes dataframe
    const nodesData = Array.from(groupedMap.entries()).map(([title, textUnitIds]) => ({
        title: title,
        frequency: textUnitIds.length,
        text_unit_ids: textUnitIds
    }));

    return {
        columns: ['title', 'frequency', 'text_unit_ids'],
        data: nodesData
    };
}

/**
 * Extract edges from nodes.
 * Nodes appear in the same text unit are connected.
 * Input: nodes_df with schema [id, title, frequency, text_unit_ids]
 * Returns: edges_df with schema [source, target, weight, text_unit_ids]
 */
function extractEdges(
    nodesDF: DataFrame,
    normalizeEdgeWeights: boolean = true
): DataFrame {
    // Explode text_unit_ids
    const explodedData: Array<{ title: string; text_unit_id: string }> = [];
    nodesDF.data.forEach(row => {
        if (Array.isArray(row.text_unit_ids)) {
            row.text_unit_ids.forEach((textUnitId: string) => {
                explodedData.push({
                    title: row.title,
                    text_unit_id: textUnitId
                });
            });
        }
    });

    // Group by text_unit_id
    const textUnitGroups = new Map<string, string[]>();
    explodedData.forEach(item => {
        if (!textUnitGroups.has(item.text_unit_id)) {
            textUnitGroups.set(item.text_unit_id, []);
        }
        textUnitGroups.get(item.text_unit_id)!.push(item.title);
    });

    // Generate combinations for each text unit (only if more than 1 title)
    const allEdges: Array<{ source: string; target: string; text_unit_id: string }> = [];
    
    textUnitGroups.forEach((titles, textUnitId) => {
        if (titles.length > 1) {
            // Generate all combinations of pairs
            for (let i = 0; i < titles.length; i++) {
                for (let j = i + 1; j < titles.length; j++) {
                    const source = titles[i] < titles[j] ? titles[i] : titles[j];
                    const target = titles[i] < titles[j] ? titles[j] : titles[i];
                    
                    allEdges.push({
                        source: source,
                        target: target,
                        text_unit_id: textUnitId
                    });
                }
            }
        }
    });

    // Filter out edges with null source or target
    const validEdges = allEdges.filter(edge => 
        edge.source != null && edge.target != null
    );

    // Group by source and target
    const edgeGroups = new Map<string, string[]>();
    validEdges.forEach(edge => {
        const key = `${edge.source}|${edge.target}`;
        if (!edgeGroups.has(key)) {
            edgeGroups.set(key, []);
        }
        edgeGroups.get(key)!.push(edge.text_unit_id);
    });

    // Create final edges dataframe
    let edgesData = Array.from(edgeGroups.entries()).map(([key, textUnitIds]) => {
        const [source, target] = key.split('|');
        return {
            source: source,
            target: target,
            weight: textUnitIds.length,
            text_unit_ids: textUnitIds
        };
    });

    let edgesDF: DataFrame = {
        columns: ['source', 'target', 'weight', 'text_unit_ids'],
        data: edgesData
    };

    if (normalizeEdgeWeights) {
        // Use PMI weight instead of raw weight
        edgesDF = calculatePmiEdgeWeights(nodesDF, edgesDF);
    }

    return edgesDF;
}