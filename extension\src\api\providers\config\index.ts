// providers/index.ts
import { deepseekConfig } from "./deepseek"
import { openaiConfig } from "./openai"
import { PROVIDER_IDS } from "../constants"
import { ProviderConfig } from "../types"
import { googleGenAIConfig } from "./google-genai"
import { moonshotConfig } from "./moonshot"
import { grokConfig } from "./grok"
import { anthropicConfig } from "./anthropic"
import { openaiCompatibleConfig } from "./openai-compatible"

export const providerConfigs: Record<string, ProviderConfig> = {
  [PROVIDER_IDS.DEEPSEEK]: deepseekConfig,
  [PROVIDER_IDS.OPENAI]: openaiConfig,
  [PROVIDER_IDS.GOOGLE_GENAI]: googleGenAIConfig,
  [PROVIDER_IDS.MOONSHOT]: moonshotConfig,
  [PROVIDER_IDS.GROK]: grokConfig,
  [PROVIDER_IDS.ANTHROPIC]: anthropicConfig,
  [PROVIDER_IDS.OPENAICOMPATIBLE]: openaiCompatibleConfig, // Ollama
  // Add other providers here as they're created
}

export const customProvidersConfigs: Record<string, ProviderConfig> = Object.fromEntries(
	Object.entries(providerConfigs)
)

// Static models from providers that don't use dynamic fetching
const staticModels = Object.values(providerConfigs)
  .filter(provider => !provider.getModels)
  .flatMap((provider) => provider.models)

// Function to get all models including dynamic ones
export async function getAllModels() {
  const allModels = [...staticModels]
  
  // Get dynamic models from providers that support it
  for (const provider of Object.values(providerConfigs)) {
    if (provider.getModels) {
      try {
        const dynamicModels = await provider.getModels()
        allModels.push(...dynamicModels)
      } catch (error) {
        console.warn(`Failed to fetch dynamic models for ${provider.id}:`, error)
        // Continue with other providers
      }
    }
  }
  
  return allModels
}

// Export static models for backward compatibility
export const models = staticModels

export type ProviderConfigs = typeof providerConfigs
