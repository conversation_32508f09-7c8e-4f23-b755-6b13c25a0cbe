import dedent from "dedent"
import { buildPromptFromTemplate, PromptConfig, promptTemplate } from "../utils/utils"
import osName from "os-name"
import defaultShell from "default-shell"
import os from "os"
import { PromptBuilder } from "../utils/builder"
import { fileEditorPrompt } from "../tools/file-editor"

const diffFixerPrompt = promptTemplate(
	(b, h) =>
		dedent`You're goal is to take the expected search and replace blocks and apply them correctly according to the latest file content and the intended changes. You should be able to identify the correct block of code that needs to be replaced and apply the correct changes to the file content. You should be able to identify the correct block of code that needs to be replaced and apply the correct changes to the file content. You should be able to identify the correct block of code that needs to be replaced and apply the correct changes to the file content.
The current search and replace blocks failed to apply due to either missing content, incorrect search blocks or incorrect context.
You need to output the entire fixed search and replace blocks with the correct changes applied to the file content.

====

TOOL USE

You have access to a set of tools that are executed upon the user's approval. You can use one tool per message, and will receive the result of that tool use in the user's response. You use tools step-by-step to accomplish a given task, with each tool use informed by the result of the previous tool use.
In the next message, you will be provided with the results of the tool, which you should firts observe with <observation></observation> tags, then think deeply using <thinking></thinking> tags, and then act on the results using the <action></action> tags, and inside the action tags you will call the next tool to continue with the task.

# Tool Use Formatting

Tool use is formatted using XML-style tags. The tool name is enclosed in opening and closing tags, and each parameter is similarly enclosed within its own set of tags. Here's the structure:

<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...</tool_name>

${b.toolSection}


====

RULES

1. First identify the correct block of code that needs to be replaced.
2. Bundle all the search and replace blocks together and apply them to the file content.
3. Fix any missing content or incorrect search blocks.
4. Apply the correct changes to the file content.
5. Output the entire fixed search and replace blocks with the correct changes applied to the file content.

====

SYSTEM INFORMATION

Operating System: ${b.osName}
Default Shell: ${b.defaultShell}
Home Directory: ${b.homeDir}
Current Working Directory: ${b.cwd}
`
)

export default () => {
	const config: PromptConfig = {
		agentName: "Quicker",
		osName: osName(),
		defaultShell: defaultShell,
		homeDir: os.homedir().replace(/\\/g, "/"),
		template: diffFixerPrompt,
	}

	const builder = new PromptBuilder(config)
	builder.addTool(fileEditorPrompt)

	const systemPrompt = builder.build()
	return systemPrompt
}
