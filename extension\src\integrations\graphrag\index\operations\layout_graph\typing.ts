/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'NodePosition' model.
 */

/**
 * Node position class definition.
 */
export interface NodePosition {
    /** Node label */
    label: string;
    /** Cluster identifier */
    cluster: string;
    /** Node size */
    size: number;
    /** X coordinate */
    x: number;
    /** Y coordinate */
    y: number;
    /** Z coordinate (optional for 3D layouts) */
    z?: number;
}

/**
 * Graph layout type - array of node positions
 */
export type GraphLayout = NodePosition[];

/**
 * Convert NodePosition to pandas-like tuple format.
 */
export function nodePositionToPandas(position: NodePosition): [string, number, number, string, number] {
    return [position.label, position.x, position.y, position.cluster, position.size];
}