/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing entity_extract methods.
 */

import { DataFrame } from '../../../data-model/types';
import { PipelineCache } from '../../../cache/pipeline-cache';
import { WorkflowCallbacks } from '../../../callbacks/workflow-callbacks';
import { AsyncType } from '../../../config/enums';
import { deriveFromRows } from '../../utils/derive-from-rows';
import { 
    Document, 
    EntityExtractStrategy, 
    ExtractEntityStrategyType,
    EntityExtractionResult 
} from './typing';

const logger = console;

const DEFAULT_ENTITY_TYPES = ["organization", "person", "geo", "event"];

/**
 * Extract a graph from a piece of text using a language model.
 */
export async function extractGraph(
    textUnits: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    textColumn: string,
    idColumn: string,
    strategy?: Record<string, any>,
    asyncMode: AsyncType = AsyncType.AsyncIO,
    entityTypes: string[] = DEFAULT_ENTITY_TYPES,
    numThreads: number = 4
): Promise<[DataFrame, DataFrame]> {
    logger.debug("entity_extract strategy=", strategy);
    
    const entityTypesToUse = entityTypes || DEFAULT_ENTITY_TYPES;
    const strategyConfig = { ...strategy };
    const strategyExec = loadStrategy(
        strategy?.type || ExtractEntityStrategyType.graph_intelligence
    );

    let numStarted = 0;

    async function runStrategy(row: Record<string, any>): Promise<[any[], any[], any] | null> {
        const text = row[textColumn];
        const id = row[idColumn];
        
        const result = await strategyExec(
            [{ text, id }],
            entityTypesToUse,
            cache,
            strategyConfig
        );
        
        numStarted += 1;
        return [result.entities, result.relationships, result.graph];
    }

    const results = await deriveFromRows(
        textUnits,
        runStrategy,
        callbacks,
        numThreads,
        asyncMode,
        "extract graph progress: "
    );

    const entityDfs: DataFrame[] = [];
    const relationshipDfs: DataFrame[] = [];
    
    for (const result of results) {
        if (result) {
            // Create DataFrames from entities and relationships
            if (result[0] && result[0].length > 0) {
                const entityColumns = Object.keys(result[0][0]);
                entityDfs.push({
                    columns: entityColumns,
                    data: result[0]
                });
            }
            
            if (result[1] && result[1].length > 0) {
                const relationshipColumns = Object.keys(result[1][0]);
                relationshipDfs.push({
                    columns: relationshipColumns,
                    data: result[1]
                });
            }
        }
    }

    const entities = mergeEntities(entityDfs);
    const relationships = mergeRelationships(relationshipDfs);

    return [entities, relationships];
}

/**
 * Load strategy method definition.
 */
function loadStrategy(strategyType: ExtractEntityStrategyType): EntityExtractStrategy {
    switch (strategyType) {
        case ExtractEntityStrategyType.graph_intelligence:
            // Import graph intelligence strategy
            return async (documents, entityTypes, cache, config) => {
                // Simplified implementation - in reality you'd implement the full strategy
                console.warn('Graph intelligence strategy not fully implemented');
                return {
                    entities: documents.map((doc, index) => ({
                        title: `Entity_${index}`,
                        type: 'organization',
                        description: `Entity extracted from ${doc.text.substring(0, 50)}...`,
                        source_id: doc.id
                    })),
                    relationships: [],
                    graph: null
                };
            };
        default:
            throw new Error(`Unknown strategy: ${strategyType}`);
    }
}

/**
 * Merge entities from multiple DataFrames.
 */
function mergeEntities(entityDfs: DataFrame[]): DataFrame {
    if (entityDfs.length === 0) {
        return {
            columns: ['title', 'type', 'description', 'text_unit_ids', 'frequency'],
            data: []
        };
    }

    // Combine all entities
    const allEntities: any[] = [];
    entityDfs.forEach(df => {
        allEntities.push(...df.data);
    });

    // Group by title and type
    const groupedEntities = new Map<string, any[]>();
    allEntities.forEach(entity => {
        const key = `${entity.title}|${entity.type}`;
        if (!groupedEntities.has(key)) {
            groupedEntities.set(key, []);
        }
        groupedEntities.get(key)!.push(entity);
    });

    // Aggregate grouped entities
    const mergedData = Array.from(groupedEntities.entries()).map(([key, entities]) => {
        const [title, type] = key.split('|');
        const descriptions: string[] = [];
        const textUnitIds: string[] = [];
        
        entities.forEach(entity => {
            if (entity.description) {
                descriptions.push(entity.description);
            }
            if (entity.source_id) {
                textUnitIds.push(entity.source_id);
            }
        });

        return {
            title,
            type,
            description: descriptions,
            text_unit_ids: textUnitIds,
            frequency: textUnitIds.length
        };
    });

    return {
        columns: ['title', 'type', 'description', 'text_unit_ids', 'frequency'],
        data: mergedData
    };
}

/**
 * Merge relationships from multiple DataFrames.
 */
function mergeRelationships(relationshipDfs: DataFrame[]): DataFrame {
    if (relationshipDfs.length === 0) {
        return {
            columns: ['source', 'target', 'description', 'text_unit_ids', 'weight'],
            data: []
        };
    }

    // Combine all relationships
    const allRelationships: any[] = [];
    relationshipDfs.forEach(df => {
        allRelationships.push(...df.data);
    });

    // Group by source and target
    const groupedRelationships = new Map<string, any[]>();
    allRelationships.forEach(relationship => {
        const key = `${relationship.source}|${relationship.target}`;
        if (!groupedRelationships.has(key)) {
            groupedRelationships.set(key, []);
        }
        groupedRelationships.get(key)!.push(relationship);
    });

    // Aggregate grouped relationships
    const mergedData = Array.from(groupedRelationships.entries()).map(([key, relationships]) => {
        const [source, target] = key.split('|');
        const descriptions: string[] = [];
        const textUnitIds: string[] = [];
        let totalWeight = 0;
        
        relationships.forEach(relationship => {
            if (relationship.description) {
                descriptions.push(relationship.description);
            }
            if (relationship.source_id) {
                textUnitIds.push(relationship.source_id);
            }
            if (relationship.weight) {
                totalWeight += relationship.weight;
            }
        });

        return {
            source,
            target,
            description: descriptions,
            text_unit_ids: textUnitIds,
            weight: totalWeight
        };
    });

    return {
        columns: ['source', 'target', 'description', 'text_unit_ids', 'weight'],
        data: mergedData
    };
}