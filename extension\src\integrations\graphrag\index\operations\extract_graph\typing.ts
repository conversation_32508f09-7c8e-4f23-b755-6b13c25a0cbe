/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing 'Document' and 'EntityExtractionResult' models.
 */

import { Graph } from '../../utils/graphs';
import { PipelineCache } from '../../../cache/pipeline-cache';

export type ExtractedEntity = Record<string, any>;
export type ExtractedRelationship = Record<string, any>;
export type StrategyConfig = Record<string, any>;
export type EntityTypes = string[];

/**
 * Document class definition.
 */
export interface Document {
    text: string;
    id: string;
}

/**
 * Entity extraction result class definition.
 */
export interface EntityExtractionResult {
    entities: ExtractedEntity[];
    relationships: ExtractedRelationship[];
    graph: Graph | null;
}

/**
 * Entity extract strategy function type
 */
export type EntityExtractStrategy = (
    documents: Document[],
    entityTypes: EntityTypes,
    cache: PipelineCache,
    config: StrategyConfig
) => Promise<EntityExtractionResult>;

/**
 * ExtractEntityStrategyType enum definition.
 */
export enum ExtractEntityStrategyType {
    graph_intelligence = "graph_intelligence",
    nltk = "nltk"
}