// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Query Engine API.
 * 
 * This API provides access to the query engine of graphrag, allowing external applications
 * to hook into graphrag and run queries over a knowledge graph generated by graphrag.
 * 
 * Contains the following functions:
 *  - globalSearch: Perform a global search.
 *  - globalSearchStreaming: Perform a global search and stream results back.
 *  - localSearch: Perform a local search.
 *  - localSearchStreaming: Perform a local search and stream results back.
 * 
 * WARNING: This API is under development and may undergo changes in future releases.
 * Backwards compatibility is not guaranteed at this time.
 */

import { NoopQueryCallbacks } from '../callbacks/noop-query-callbacks';
import { QueryCallbacks } from '../callbacks/query-callbacks';
import {
    communityFullContentEmbedding,
    entityDescriptionEmbedding,
    textUnitTextEmbedding,
} from '../config/embeddings';
import { GraphRagConfig } from '../config/models/graph-rag-config';
import { initLoggers } from '../logger/standard-logging';
import {
    getBasicSearchEngine,
    getDriftSearchEngine,
    getGlobalSearchEngine,
    getLocalSearchEngine,
} from '../query/factory';
import {
    readIndexerCommunities,
    readIndexerCovariates,
    readIndexerEntities,
    readIndexerRelationships,
    readIndexerReportEmbeddings,
    readIndexerReports,
    readIndexerTextUnits,
} from '../query/indexer-adapters';
import {
    getEmbeddingStore,
    loadSearchPrompt,
    truncate,
    updateContextData,
} from '../utils/api';
import { redact } from '../utils/cli';
import { DataFrame } from '../utils/storage';

// Type definitions for search results
export type SearchResult = string | Record<string, any> | Record<string, any>[];
export type ContextData = string | DataFrame[] | Record<string, DataFrame>;

/**
 * Perform a global search and return the context data and response.
 */
export async function globalSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    communityLevel?: number | null;
    dynamicCommunitySelection: boolean;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false, callbacks = [] } = options;
    
    initLoggers({ config, verbose });

    let fullResponse = "";
    let contextData: any = {};

    function onContext(context: any): void {
        contextData = context;
    }

    const localCallbacks = new NoopQueryCallbacks();
    localCallbacks.onContext = onContext;
    callbacks.push(localCallbacks);

    console.debug(`Executing global search query: ${options.query}`);
    
    for await (const chunk of globalSearchStreaming(options)) {
        fullResponse += chunk;
    }
    
    console.debug(`Query response: ${truncate(fullResponse, 400)}`);
    return [fullResponse, contextData];
}

/**
 * Perform a global search and return the context data and response via a generator.
 */
export async function* globalSearchStreaming(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    communityLevel?: number | null;
    dynamicCommunitySelection: boolean;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): AsyncGenerator<string, void, unknown> {
    const { config, verbose = false } = options;
    
    initLoggers({ config, verbose });

    const communities_ = readIndexerCommunities(options.communities, options.communityReports);
    const reports = readIndexerReports(
        options.communityReports,
        options.communities,
        options.communityLevel,
        options.dynamicCommunitySelection
    );
    const entities_ = readIndexerEntities(
        options.entities,
        options.communities,
        options.communityLevel
    );
    
    const mapPrompt = loadSearchPrompt(config.root_dir, config.global_search.map_prompt);
    const reducePrompt = loadSearchPrompt(config.root_dir, config.global_search.reduce_prompt);
    const knowledgePrompt = loadSearchPrompt(config.root_dir, config.global_search.knowledge_prompt);

    console.debug(`Executing streaming global search query: ${options.query}`);
    
    const searchEngine = getGlobalSearchEngine({
        config,
        reports,
        entities: entities_,
        communities: communities_,
        responseType: options.responseType,
        dynamicCommunitySelection: options.dynamicCommunitySelection,
        mapSystemPrompt: mapPrompt,
        reduceSystemPrompt: reducePrompt,
        generalKnowledgeInclusionPrompt: knowledgePrompt,
        callbacks: options.callbacks,
    });
    
    yield* searchEngine.streamSearch(options.query);
}

/**
 * Perform a local search and return the context data and response.
 */
export async function localSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    textUnits: DataFrame;
    relationships: DataFrame;
    covariates?: DataFrame | null;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false, callbacks = [] } = options;
    
    initLoggers({ config, verbose });

    let fullResponse = "";
    let contextData: any = {};

    function onContext(context: any): void {
        contextData = context;
    }

    const localCallbacks = new NoopQueryCallbacks();
    localCallbacks.onContext = onContext;
    callbacks.push(localCallbacks);

    console.debug(`Executing local search query: ${options.query}`);
    
    for await (const chunk of localSearchStreaming(options)) {
        fullResponse += chunk;
    }
    
    console.debug(`Query response: ${truncate(fullResponse, 400)}`);
    return [fullResponse, contextData];
}

/**
 * Perform a local search and return the context data and response via a generator.
 */
export async function* localSearchStreaming(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    textUnits: DataFrame;
    relationships: DataFrame;
    covariates?: DataFrame | null;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): AsyncGenerator<string, void, unknown> {
    const { config, verbose = false } = options;
    
    initLoggers({ config, verbose });

    const vectorStoreArgs: Record<string, any> = {};
    for (const [index, store] of Object.entries(config.vector_store)) {
        vectorStoreArgs[index] = { ...store };
    }
    const msg = `Vector Store Args: ${redact(vectorStoreArgs)}`;
    console.debug(msg);

    const descriptionEmbeddingStore = getEmbeddingStore(
        vectorStoreArgs,
        entityDescriptionEmbedding
    );

    const entities_ = readIndexerEntities(options.entities, options.communities, options.communityLevel);
    const covariates_ = options.covariates ? readIndexerCovariates(options.covariates) : [];
    const prompt = loadSearchPrompt(config.root_dir, config.local_search.prompt);

    console.debug(`Executing streaming local search query: ${options.query}`);
    
    const searchEngine = getLocalSearchEngine({
        config,
        reports: readIndexerReports(options.communityReports, options.communities, options.communityLevel),
        textUnits: readIndexerTextUnits(options.textUnits),
        entities: entities_,
        relationships: readIndexerRelationships(options.relationships),
        covariates: { claims: covariates_ },
        descriptionEmbeddingStore,
        responseType: options.responseType,
        systemPrompt: prompt,
        callbacks: options.callbacks,
    });
    
    yield* searchEngine.streamSearch(options.query);
}

/**
 * Perform a DRIFT search and return the context data and response.
 */
export async function driftSearch(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    textUnits: DataFrame;
    relationships: DataFrame;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false, callbacks = [] } = options;
    
    initLoggers({ config, verbose });

    let fullResponse = "";
    let contextData: any = {};

    function onContext(context: any): void {
        contextData = context;
    }

    const localCallbacks = new NoopQueryCallbacks();
    localCallbacks.onContext = onContext;
    callbacks.push(localCallbacks);

    console.debug(`Executing DRIFT search query: ${options.query}`);
    
    for await (const chunk of driftSearchStreaming(options)) {
        fullResponse += chunk;
    }
    
    console.debug(`Query response: ${truncate(fullResponse, 400)}`);
    return [fullResponse, contextData];
}

/**
 * Perform a DRIFT search and return the context data and response via a generator.
 */
export async function* driftSearchStreaming(options: {
    config: GraphRagConfig;
    entities: DataFrame;
    communities: DataFrame;
    communityReports: DataFrame;
    textUnits: DataFrame;
    relationships: DataFrame;
    communityLevel: number;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): AsyncGenerator<string, void, unknown> {
    const { config, verbose = false } = options;
    
    initLoggers({ config, verbose });

    // Mock implementation - in real version would implement full DRIFT search
    console.debug(`Executing streaming DRIFT search query: ${options.query}`);
    yield "DRIFT search not fully implemented in TypeScript version";
}

/**
 * Perform a basic search and return the context data and response.
 */
export async function basicSearch(options: {
    config: GraphRagConfig;
    textUnits: DataFrame;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): Promise<[SearchResult, ContextData]> {
    const { config, verbose = false, callbacks = [] } = options;
    
    initLoggers({ config, verbose });

    let fullResponse = "";
    let contextData: any = {};

    function onContext(context: any): void {
        contextData = context;
    }

    const localCallbacks = new NoopQueryCallbacks();
    localCallbacks.onContext = onContext;
    callbacks.push(localCallbacks);

    console.debug(`Executing basic search query: ${options.query}`);
    
    for await (const chunk of basicSearchStreaming(options)) {
        fullResponse += chunk;
    }
    
    console.debug(`Query response: ${truncate(fullResponse, 400)}`);
    return [fullResponse, contextData];
}

/**
 * Perform a basic search and return the context data and response via a generator.
 */
export async function* basicSearchStreaming(options: {
    config: GraphRagConfig;
    textUnits: DataFrame;
    responseType: string;
    query: string;
    callbacks?: QueryCallbacks[] | null;
    verbose?: boolean;
}): AsyncGenerator<string, void, unknown> {
    const { config, verbose = false } = options;
    
    initLoggers({ config, verbose });

    const vectorStoreArgs: Record<string, any> = {};
    for (const [index, store] of Object.entries(config.vector_store)) {
        vectorStoreArgs[index] = { ...store };
    }

    const textEmbeddingStore = getEmbeddingStore(
        vectorStoreArgs,
        textUnitTextEmbedding
    );

    const prompt = loadSearchPrompt(config.root_dir, config.basic_search.prompt);

    console.debug(`Executing streaming basic search query: ${options.query}`);
    
    const searchEngine = getBasicSearchEngine({
        config,
        textUnits: readIndexerTextUnits(options.textUnits),
        textEmbeddingStore,
        responseType: options.responseType,
        systemPrompt: prompt,
        callbacks: options.callbacks,
    });
    
    yield* searchEngine.streamSearch(options.query);
}

// Multi-index search functions would be implemented similarly
// For brevity, providing simplified versions

export async function multiIndexGlobalSearch(options: any): Promise<[SearchResult, ContextData]> {
    throw new Error("Multi-index global search not yet implemented in TypeScript version");
}

export async function multiIndexLocalSearch(options: any): Promise<[SearchResult, ContextData]> {
    throw new Error("Multi-index local search not yet implemented in TypeScript version");
}

export async function multiIndexDriftSearch(options: any): Promise<[SearchResult, ContextData]> {
    throw new Error("Multi-index DRIFT search not yet implemented in TypeScript version");
}

export async function multiIndexBasicSearch(options: any): Promise<[SearchResult, ContextData]> {
    throw new Error("Multi-index basic search not yet implemented in TypeScript version");
}