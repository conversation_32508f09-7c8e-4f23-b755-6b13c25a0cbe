/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the readDotenv utility.
 */

import * as fs from 'fs';
import * as path from 'path';

const logger = console;

/**
 * Read a .env file in the given root path.
 */
export function readDotenv(root: string): void {
    const envPath = path.join(root, '.env');
    
    if (fs.existsSync(envPath)) {
        logger.info("Loading pipeline .env file");
        
        try {
            const envContent = fs.readFileSync(envPath, 'utf-8');
            const envConfig = parseDotenv(envContent);
            
            for (const [key, value] of Object.entries(envConfig)) {
                if (!(key in process.env)) {
                    process.env[key] = value || '';
                }
            }
        } catch (error) {
            logger.error(`Error reading .env file: ${error}`);
        }
    } else {
        logger.info(`No .env file found at ${root}`);
    }
}

/**
 * Parse .env file content into key-value pairs.
 */
function parseDotenv(content: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = content.split('\n');
    
    for (const line of lines) {
        const trimmedLine = line.trim();
        
        // Skip empty lines and comments
        if (!trimmedLine || trimmedLine.startsWith('#')) {
            continue;
        }
        
        const equalIndex = trimmedLine.indexOf('=');
        if (equalIndex === -1) {
            continue;
        }
        
        const key = trimmedLine.substring(0, equalIndex).trim();
        let value = trimmedLine.substring(equalIndex + 1).trim();
        
        // Remove quotes if present
        if ((value.startsWith('"') && value.endsWith('"')) ||
            (value.startsWith("'") && value.endsWith("'"))) {
            value = value.slice(1, -1);
        }
        
        result[key] = value;
    }
    
    return result;
}