/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Base class for noun phrase extractors.
 */

const logger = console;

/**
 * Abstract base class for noun phrase extractors.
 */
export abstract class BaseNounPhraseExtractor {
    protected modelName?: string;
    protected maxWordLength: number;
    protected excludeNouns: string[];
    protected wordDelimiter: string;

    constructor(
        modelName?: string,
        excludeNouns?: string[],
        maxWordLength: number = 15,
        wordDelimiter: string = " "
    ) {
        this.modelName = modelName;
        this.maxWordLength = maxWordLength;
        this.excludeNouns = (excludeNouns || []).map(noun => noun.toUpperCase());
        this.wordDelimiter = wordDelimiter;
    }

    /**
     * Extract noun phrases from text.
     * @param text - Text to extract noun phrases from
     * @returns List of noun phrases
     */
    abstract extract(text: string): string[];

    /**
     * Return string representation of the extractor, used for cache key generation.
     */
    abstract toString(): string;

    /**
     * Load a SpaCy-like model (simplified implementation).
     * Note: In TypeScript, you would use libraries like compromise, natural, or spacy-js.
     */
    protected static loadSpacyModel(
        modelName: string,
        exclude?: string[]
    ): any {
        // Simplified implementation - in reality you'd use a proper NLP library
        logger.warn(`SpaCy model loading not fully implemented. Model: ${modelName}`);
        return {
            process: (text: string) => {
                // Simple word tokenization as placeholder
                return text.split(/\s+/).map(word => ({
                    text: word,
                    pos_: 'NOUN', // Simplified POS tagging
                    ent_type_: '',
                    lemma_: word.toLowerCase()
                }));
            }
        };
    }
}