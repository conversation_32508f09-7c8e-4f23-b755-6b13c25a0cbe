// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Context builders for text units.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';
import { buildMixedContext } from '../build-mixed-context.js';
import { prepTextUnits } from './prep-text-units.js';
import { sortContext } from './sort-context.js';
import { numTokens } from '../../../../query/llm/text-utils.js';

/**
 * Prep context data for community report generation using text unit data.
 * 
 * Community membership has columns [COMMUNITY_ID, COMMUNITY_LEVEL, ENTITY_IDS, RELATIONSHIP_IDS, TEXT_UNIT_IDS]
 */
export function buildLocalContext(
  communityMembershipDf: DataFrame,
  textUnitsDf: DataFrame,
  nodeDf: DataFrame,
  maxContextTokens: number = 16000
): DataFrame {
  // Get text unit details, include short_id, text, and entity degree
  let preppedTextUnitsDf = prepTextUnits(textUnitsDf, nodeDf);
  preppedTextUnitsDf = preppedTextUnitsDf.rename({
    [schemas.ID]: schemas.TEXT_UNIT_IDS,
    [schemas.COMMUNITY_ID]: schemas.COMMUNITY_ID,
  });

  // Merge text unit details with community membership
  let contextDf = communityMembershipDf.select([
    schemas.COMMUNITY_ID,
    schemas.COMMUNITY_LEVEL,
    schemas.TEXT_UNIT_IDS
  ]);
  
  contextDf = contextDf.explode(schemas.TEXT_UNIT_IDS);
  contextDf = contextDf.merge(
    preppedTextUnitsDf,
    [schemas.TEXT_UNIT_IDS, schemas.COMMUNITY_ID],
    'left'
  );

  contextDf = contextDf.withColumn(
    schemas.ALL_CONTEXT,
    contextDf.toRecords().map(row => ({
      id: row[schemas.ALL_DETAILS][schemas.SHORT_ID],
      text: row[schemas.ALL_DETAILS][schemas.TEXT],
      entity_degree: row[schemas.ALL_DETAILS][schemas.ENTITY_DEGREE],
    }))
  );

  contextDf = contextDf
    .groupBy([schemas.COMMUNITY_ID, schemas.COMMUNITY_LEVEL])
    .agg({ [schemas.ALL_CONTEXT]: 'collect_list' });

  contextDf = contextDf
    .withColumn(
      schemas.CONTEXT_STRING,
      contextDf.getColumn(schemas.ALL_CONTEXT).map(x => sortContext(x))
    )
    .withColumn(
      schemas.CONTEXT_SIZE,
      contextDf.getColumn(schemas.CONTEXT_STRING).map(x => numTokens(x))
    )
    .withColumn(
      schemas.CONTEXT_EXCEED_FLAG,
      contextDf.getColumn(schemas.CONTEXT_SIZE).map(x => x > maxContextTokens)
    );

  return contextDf;
}

/**
 * Prep context for each community in a given level.
 */
export function buildLevelContext(
  reportDf: DataFrame | undefined,
  communityHierarchyDf: DataFrame,
  localContextDf: DataFrame,
  level: number,
  maxContextTokens: number = 16000
): DataFrame {
  if (!reportDf || reportDf.isEmpty()) {
    // No report to substitute with, just trim the local context
    const levelContextDf = localContextDf.filter(
      row => row[schemas.COMMUNITY_LEVEL] === level
    );

    const validContextDf = levelContextDf.filter(
      row => !row[schemas.CONTEXT_EXCEED_FLAG]
    );
    const invalidContextDf = levelContextDf.filter(
      row => row[schemas.CONTEXT_EXCEED_FLAG]
    );

    if (invalidContextDf.isEmpty()) {
      return validContextDf;
    }

    const updatedInvalidDf = invalidContextDf
      .withColumn(
        schemas.CONTEXT_STRING,
        invalidContextDf.getColumn(schemas.ALL_CONTEXT).map(x =>
          sortContext(x, maxContextTokens)
        )
      )
      .withColumn(
        schemas.CONTEXT_SIZE,
        invalidContextDf.getColumn(schemas.CONTEXT_STRING).map(x => numTokens(x))
      )
      .withColumn(schemas.CONTEXT_EXCEED_FLAG, false);

    return DataFrame.concat([validContextDf, updatedInvalidDf]);
  }

  let levelContextDf = localContextDf.filter(
    row => row[schemas.COMMUNITY_LEVEL] === level
  );

  // Exclude those that already have reports
  levelContextDf = levelContextDf.antijoin(
    reportDf.select([schemas.COMMUNITY_ID]),
    schemas.COMMUNITY_ID
  );

  const validContextDf = levelContextDf.filter(
    row => row[schemas.CONTEXT_EXCEED_FLAG] === false
  );
  const invalidContextDf = levelContextDf.filter(
    row => row[schemas.CONTEXT_EXCEED_FLAG] === true
  );

  if (invalidContextDf.isEmpty()) {
    return validContextDf;
  }

  // Try to substitute with sub-community reports
  const subReportDf = reportDf
    .filter(row => row[schemas.COMMUNITY_LEVEL] === level + 1)
    .drop([schemas.COMMUNITY_LEVEL]);

  let subContextDf = localContextDf.filter(
    row => row[schemas.COMMUNITY_LEVEL] === level + 1
  );
  
  subContextDf = subContextDf.merge(subReportDf, schemas.COMMUNITY_ID, 'left');
  subContextDf = subContextDf.rename({
    [schemas.COMMUNITY_ID]: schemas.SUB_COMMUNITY
  });

  // Collect all sub communities' contexts for each community
  let communityDf = communityHierarchyDf
    .filter(row => row[schemas.COMMUNITY_LEVEL] === level)
    .drop([schemas.COMMUNITY_LEVEL]);

  communityDf = communityDf.merge(
    invalidContextDf.select([schemas.COMMUNITY_ID]),
    schemas.COMMUNITY_ID,
    'inner'
  );

  communityDf = communityDf.merge(
    subContextDf.select([
      schemas.SUB_COMMUNITY,
      schemas.FULL_CONTENT,
      schemas.ALL_CONTEXT,
      schemas.CONTEXT_SIZE,
    ]),
    schemas.SUB_COMMUNITY,
    'left'
  );

  communityDf = communityDf.withColumn(
    schemas.ALL_CONTEXT,
    communityDf.toRecords().map(row => ({
      [schemas.SUB_COMMUNITY]: row[schemas.SUB_COMMUNITY],
      [schemas.ALL_CONTEXT]: row[schemas.ALL_CONTEXT],
      [schemas.FULL_CONTENT]: row[schemas.FULL_CONTENT],
      [schemas.CONTEXT_SIZE]: row[schemas.CONTEXT_SIZE],
    }))
  );

  communityDf = communityDf
    .groupBy(schemas.COMMUNITY_ID)
    .agg({ [schemas.ALL_CONTEXT]: 'collect_list' });

  communityDf = communityDf
    .withColumn(
      schemas.CONTEXT_STRING,
      communityDf.getColumn(schemas.ALL_CONTEXT).map(x =>
        buildMixedContext(x, maxContextTokens)
      )
    )
    .withColumn(
      schemas.CONTEXT_SIZE,
      communityDf.getColumn(schemas.CONTEXT_STRING).map(x => numTokens(x))
    )
    .withColumn(schemas.CONTEXT_EXCEED_FLAG, false)
    .withColumn(schemas.COMMUNITY_LEVEL, level);

  // Handle any remaining invalid records
  const remainingDf = invalidContextDf
    .antijoin(communityDf.select([schemas.COMMUNITY_ID]), schemas.COMMUNITY_ID)
    .withColumn(
      schemas.CONTEXT_STRING,
      invalidContextDf.getColumn(schemas.ALL_CONTEXT).map(x =>
        sortContext(x, maxContextTokens)
      )
    )
    .withColumn(
      schemas.CONTEXT_SIZE,
      invalidContextDf.getColumn(schemas.CONTEXT_STRING).map(x => numTokens(x))
    )
    .withColumn(schemas.CONTEXT_EXCEED_FLAG, false);

  return DataFrame.concat([validContextDf, communityDf, remainingDf]);
}
