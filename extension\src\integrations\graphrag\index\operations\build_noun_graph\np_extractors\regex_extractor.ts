/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Functions to analyze text data using regex patterns.
 */

import { BaseNounPhraseExtractor } from './base';

/**
 * Regular expression-based noun phrase extractor for English.
 */
export class RegexENNounPhraseExtractor extends BaseNounPhraseExtractor {
    constructor(
        excludeNouns: string[],
        maxWordLength: number,
        wordDelimiter: string
    ) {
        super(undefined, excludeNouns, maxWordLength, wordDelimiter);
        
        // Note: In TypeScript, you would use alternative libraries
        // like 'natural', 'compromise', or other NLP libraries
        console.warn('NLTK and TextBlob dependencies not available in TypeScript. Using simplified implementation.');
    }

    /**
     * Extract noun phrases from text using regex patterns.
     * Note: This is a simplified implementation. In production, use proper NLP libraries.
     */
    extract(text: string): string[] {
        // Simplified noun phrase extraction using regex patterns
        // In a real implementation, you would use libraries like compromise or natural
        
        // Simple pattern to match potential noun phrases
        const nounPhrasePattern = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g;
        const matches = text.match(nounPhrasePattern) || [];
        
        const properNouns = this.extractProperNouns(text);
        const taggedNounPhrases = matches.map(chunk => 
            this.tagNounPhrases(chunk, properNouns)
        );

        const filteredNounPhrases = new Set<string>();
        
        for (const taggedNp of taggedNounPhrases) {
            if ((taggedNp.hasProperNouns || 
                 taggedNp.cleanedTokens.length > 1 || 
                 taggedNp.hasCompoundWords) && 
                taggedNp.hasValidTokens) {
                filteredNounPhrases.add(taggedNp.cleanedText);
            }
        }
        
        return Array.from(filteredNounPhrases);
    }

    /**
     * Extract proper nouns from text (simplified implementation).
     */
    private extractProperNouns(text: string): string[] {
        // Simple pattern to match capitalized words
        const properNounPattern = /\b[A-Z][a-z]+\b/g;
        const matches = text.match(properNounPattern) || [];
        return matches.map(match => match.toUpperCase());
    }

    /**
     * Extract attributes of a noun chunk, to be used for filtering.
     */
    private tagNounPhrases(
        nounPhrase: string, 
        allProperNouns: string[] = []
    ): {
        cleanedTokens: string[];
        cleanedText: string;
        hasProperNouns: boolean;
        hasCompoundWords: boolean;
        hasValidTokens: boolean;
    } {
        const tokens = nounPhrase.split(/\s+/).filter(token => token.length > 0);
        const cleanedTokens = tokens.filter(token => 
            !this.excludeNouns.includes(token.toUpperCase())
        );
        
        const hasProperNouns = cleanedTokens.some(token => 
            allProperNouns.includes(token.toUpperCase())
        );
        
        const hasCompoundWords = cleanedTokens.some(token => 
            token.includes('-') && 
            token.trim().length > 1 && 
            token.trim().split('-').length > 1
        );
        
        const hasValidTokens = cleanedTokens.every(token => 
            /^[a-zA-Z0-9\-]+\n?$/.test(token)
        ) && cleanedTokens.every(token => 
            token.length <= this.maxWordLength
        );
        
        const cleanedText = cleanedTokens
            .join(this.wordDelimiter)
            .replace(/\n/g, '')
            .toUpperCase();

        return {
            cleanedTokens,
            cleanedText,
            hasProperNouns,
            hasCompoundWords,
            hasValidTokens
        };
    }

    /**
     * Return string representation of the extractor, used for cache key generation.
     */
    toString(): string {
        return `regex_en_${this.excludeNouns.join(',')}_${this.maxWordLength}_${this.wordDelimiter}`;
    }
}