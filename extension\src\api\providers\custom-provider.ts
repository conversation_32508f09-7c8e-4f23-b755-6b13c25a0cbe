import { <PERSON>pi<PERSON><PERSON><PERSON>or<PERSON><PERSON><PERSON>, Api<PERSON>and<PERSON>, ApiHandlerOptions } from ".."
import { SSEResponse } from "../../agent/v1/task-executor/task-executor"
import { CoreMessage, LanguageModel, LanguageModelV1, smoothStream, streamText } from "ai"
import { createDeepSeek } from "@ai-sdk/deepseek"
import { createOpenAI } from "@ai-sdk/openai"

import { createAnthropic } from "@ai-sdk/anthropic"
import { createGoogleGenerativeAI } from "@ai-sdk/google"
import { convertToAISDKFormat } from "../../utils/ai-sdk-format"
import { customProviderSchema, ModelInfo } from "./types"
import { PROVIDER_IDS } from "./constants"

import { version } from "../../../package.json"
import { z } from "zod"
import { GlobalState, GlobalStateManager } from "../../providers/state/global-state-manager"
import delay from "delay"

type ExtractCacheTokens = {
	cacheCreationField: string
	cacheReadField: string
	object: Object
}

const extractCacheTokens = ({ cacheCreationField, cacheReadField, object }: ExtractCacheTokens) => {
	const cacheSchema = z.object({
		[cacheCreationField]: z.number().nullable(),
		[cacheReadField]: z.number().nullable(),
	})
	const cache = cacheSchema.safeParse(object)
	if (!cache.success) {
		return {
			cache_creation_input_tokens: null,
			cache_read_input_tokens: null,
		}
	}
	return {
		cache_creation_input_tokens: cache.data[cacheCreationField],
		cache_read_input_tokens: cache.data[cacheReadField],
	}
}

export class CustomProviderError extends Error {
	private _providerId: string
	private _modelId: string
	constructor(message: string, providerId: string, modelId: string) {
		super(message)
		this.name = "CustomProviderError"
		this._providerId = providerId
		this._modelId = modelId
	}

	get providerId() {
		return this._providerId
	}
	get modelId() {
		return this._modelId
	}
}

const providerToAISDKModel = (settings: ApiConstructorOptions, modelId: string): LanguageModelV1 => {
	switch (settings.providerSettings.providerId) {
		case PROVIDER_IDS.ANTHROPIC:
			if (!settings.providerSettings.apiKey) {
				throw new CustomProviderError(
					"Anthropic Missing API key",
					settings.providerSettings.providerId,
					modelId
				)
			}
			return createAnthropic({
				apiKey: settings.providerSettings.apiKey,
			}).languageModel(modelId)
		case PROVIDER_IDS.DEEPSEEK:
			if (!settings.providerSettings.apiKey) {
				throw new CustomProviderError("Deepseek Missing API key", settings.providerSettings.providerId, modelId)
			}
			return createDeepSeek({
				apiKey: settings.providerSettings.apiKey,
			}).languageModel(modelId)

		case PROVIDER_IDS.OPENAI:
			if (!settings.providerSettings.apiKey) {
				throw new CustomProviderError("OpenAI Missing API key", settings.providerSettings.providerId, modelId)
			}
			const openaiModelId = modelId.includes("o3-mini") ? "o3-mini" : modelId
			const reasoningEffort = modelId.includes("o3-mini")
				? {
					reasoningEffort: modelId.includes("high") ? ("high" as const) : ("medium" as const),
				}
				: undefined
			return createOpenAI({
				apiKey: settings.providerSettings.apiKey,
				compatibility: "strict",
			}).languageModel(openaiModelId, reasoningEffort)
		case PROVIDER_IDS.GOOGLE_GENAI:
			if (!settings.providerSettings.apiKey) {
				throw new CustomProviderError(
					"Google GenerativeAI Missing API key",
					settings.providerSettings.providerId,
					modelId
				)
			}
			return createGoogleGenerativeAI({
				apiKey: settings.providerSettings.apiKey,
			}).languageModel(modelId)
		case PROVIDER_IDS.MOONSHOT:
			if (!settings.providerSettings.apiKey) {
				throw new CustomProviderError("Moonshot Missing API key", settings.providerSettings.providerId, modelId)
			}
			return createOpenAI({
				apiKey: settings.providerSettings.apiKey,
				baseURL: "https://api.moonshot.cn/v1",
				compatibility: "compatible",
				headers: {
					"User-Agent": `Quicker/${version}`,
				},
			}).languageModel(modelId)
		case PROVIDER_IDS.GROK:
			if (!settings.providerSettings.apiKey) {
				throw new CustomProviderError("xAI Grok Missing API key", settings.providerSettings.providerId, modelId)
			}
			return createOpenAI({
				apiKey: settings.providerSettings.apiKey,
				baseURL: "https://api.x.ai/v1",
				compatibility: "compatible",
				headers: {
					"User-Agent": `Quicker/${version}`,
				},
			}).languageModel(modelId)
		case PROVIDER_IDS.OPENAICOMPATIBLE:
			const providerSettings = customProviderSchema.safeParse(settings.providerSettings)
			if (!providerSettings.success) {
				throw new CustomProviderError(
					"OpenAI Compatible Missing API key",
					settings.providerSettings.providerId,
					modelId
				)
			}
			return createOpenAI({
				apiKey: providerSettings.data.apiKey,
				compatibility: "compatible",
				baseURL: providerSettings.data.baseUrl,
				name: providerSettings.data.modelId,
				headers: {
					"User-Agent": `Quicker/${version}`,
				},
			}).languageModel(modelId)

		default:
			throw new CustomProviderError("Provider not configured", settings.providerSettings.providerId, modelId)
	}
}

export class CustomApiHandler implements ApiHandler {
	private _options: ApiConstructorOptions
	private abortController: AbortController | null = null

	get options() {
		return this._options
	}

	constructor(options: ApiConstructorOptions) {
		this._options = options
	}

	async abortRequest(): Promise<void> {
		if (this.abortController) {
			this.abortController.abort("Request aborted by user")
			this.abortController = null
		}
	}

	async *createMessageStream({
		messages,
		systemPrompt,
		top_p,
		tempature,
		abortSignal,
		modelId,
		appendAfterCacheToLastMessage,
		updateAfterCacheInserts,
	}: Parameters<ApiHandler["createMessageStream"]>[0]): AsyncIterableIterator<SSEResponse> {
		const convertedMessages: CoreMessage[] = []
		let thinkingConfig: GlobalState["thinking"] | undefined
		if (abortSignal?.aborted) {
			throw new Error("Request aborted by user")
		}
		let isThinkingAnthropic = false
		let isThinkingKimi = false
		if (
			modelId.includes("claude-3-7") ||
			modelId.includes("claude-3.7") ||
			modelId === "anthropic/claude-3.7-sonnet:thinking" ||
			modelId.includes("kimi-vl-a3b-thinking") // Kimi thinking models are compatible with Anthropic thinking config
		) {
			const globalStateManager = GlobalStateManager.getInstance()
			const thinking = globalStateManager.getGlobalState("thinking")
			if (thinking) {
				console.log(`Thinking config found: ${JSON.stringify(thinking)}`)
				// If thinking is enabled, set appropriate temperature
				thinkingConfig = thinking
				if (thinkingConfig.type === "enabled") {
					if (modelId.includes("claude") || modelId === "anthropic/claude-3.7-sonnet:thinking") {
						tempature = 1 // Claude thinking models require temperature = 1
						isThinkingAnthropic = true
					} else if (modelId.includes("kimi-vl-a3b-thinking")) {
						tempature = 0.8 // Kimi thinking models use temperature = 0.8 per official docs
						isThinkingKimi = true
					}
					thinking.budget_tokens = thinking.budget_tokens ?? 32_000
				}
			}
		}
		for (const systemMsg of systemPrompt) {
			convertedMessages.push({
				role: "system",
				content: systemMsg.trim(),
				// if it's the last or before last message, make it ephemeral
			})
		}

		const convertedMessagesFull = convertedMessages.concat(convertToAISDKFormat(messages))
		const currentModel = this._options.models.find((m) => m.id === modelId) ?? this._options.model

		// Special handling for Moonshot vision models - validate image token consumption
		if (currentModel.provider === PROVIDER_IDS.MOONSHOT && currentModel.supportsImages) {
			let imageCount = 0
			for (const message of convertedMessagesFull) {
				if (message.role === "user" && Array.isArray(message.content)) {
					for (const content of message.content) {
						if (typeof content === "object" && content.type === "image") {
							imageCount++
						}
					}
				}
			}
			
			// Each image consumes 1024 tokens in Moonshot vision models (official specification)
			const imageTokensPerImage = 1024
			
			const imageTokens = imageCount * imageTokensPerImage
			const availableTokens = currentModel.contextWindow - imageTokens
			
			// Reserve at least 2000 tokens for text and system prompts
			const minTextTokens = 2000
			
			if (availableTokens < minTextTokens) {
				throw new Error(`Too many images for ${currentModel.name}. ${imageCount} images consume ${imageTokens} tokens, leaving only ${availableTokens} tokens for text. Please reduce the number of images or use a text-only model.`)
			}
		}

		if (
			currentModel.supportsPromptCache &&
			(currentModel.provider === "anthropic" || currentModel.id.includes("anthropic"))
		) {
			// we want to add prompt caching
			let index = 0
			let lastSystemIndex = -1
			let lastUserIndex = -1
			let secondLastUserIndex = -1
			for (const msg of convertedMessagesFull) {
				// first find the last system message
				if (msg.role === "system") {
					lastSystemIndex = index
				}
				// find the last user message
				if (msg.role === "user") {
					secondLastUserIndex = lastUserIndex
					lastUserIndex = index
				}

				index++
			}
			// now find all the indexes and add cache control
			const addCacheControl = (indexs: number[]) => {
				for (const index of indexs) {
					const item = convertedMessagesFull[index]
					if (item) {
						item.providerOptions = {
							anthropic: { cacheControl: { type: "ephemeral" } },
						}
					}
				}
			}
			addCacheControl([lastSystemIndex, lastUserIndex, secondLastUserIndex])
		}
		// const refetchSignal = new SmartAbortSignal(5000)
		// Clamp temperature for Moonshot API (range [0, 1] instead of [0, 2])
		let finalTemperature = tempature ?? 0.1
		if (currentModel.provider === PROVIDER_IDS.MOONSHOT && finalTemperature > 1) {
			finalTemperature = 1
		}

		const result = streamText({
			...(!isThinkingAnthropic && !isThinkingKimi
				? {}
				: thinkingConfig
					? { providerOptions: { anthropic: { thinking: thinkingConfig } } }
					: {}),
			providerOptions: {
				anthropic: {
					// this is the default
					...(!thinkingConfig
						? {}
						: {
							thinking: { type: "enabled", budgetTokens: 12000 },
						}),
				},
			},
			model: providerToAISDKModel(this._options, modelId),
			// prompt: `This is a test tell me a random fact about the world`,
			messages: convertedMessagesFull,
			temperature: currentModel.id === "deepseek-reasoner" ? undefined : finalTemperature, // deepseek-reasoner doesn't support temperature, moonshot has range [0,1]
			topP: top_p ?? undefined,
			stopSequences: ["</action>"],
			abortSignal: abortSignal ?? undefined,
			experimental_transform: smoothStream(),
			maxRetries: 3,
		})

		let text = ""
		for await (const part of result.fullStream) {
			if (part.type === "error") {
				if (part.error instanceof Error) {
					yield {
						code: -1,
						body: {
							msg: part.error.message ?? "Unknown error",
							status: 500,
						},
					}
				}
			}
			if (part.type === "reasoning") {
				yield {
					code: 4,
					body: {
						reasoningDelta: part.textDelta,
					},
				}
			}
			if (part.type === "text-delta") {
				text += part.textDelta
				yield {
					code: 2,
					body: {
						text: part.textDelta,
					},
				}
			}
			if (part.type === "finish") {
				let cache_creation_input_tokens: number | null = null
				let cache_read_input_tokens: number | null = null
				if (this._options.providerSettings.providerId === PROVIDER_IDS.DEEPSEEK && part.providerMetadata) {
					; ({ cache_creation_input_tokens, cache_read_input_tokens } = extractCacheTokens({
						cacheCreationField: "promptCacheMissTokens",
						cacheReadField: "promptCacheHitTokens",
						object: part.providerMetadata["deepseek"],
					}))
				}
				if (this._options.providerSettings.providerId === PROVIDER_IDS.OPENAI) {
					const cachedPromptTokens = part.providerMetadata?.["openai"]?.cachedPromptTokens
					if (typeof cachedPromptTokens === "number") {
						cache_read_input_tokens = cachedPromptTokens
						// total_tokens - cache_read_input_tokens = cache_creation_input_tokens
						cache_creation_input_tokens = part.usage.promptTokens - cache_read_input_tokens
					}
				}
				let inputTokens =
					part.usage.promptTokens - (cache_creation_input_tokens ?? 0) - (cache_read_input_tokens ?? 0)
				if (this._options.providerSettings.providerId === PROVIDER_IDS.ANTHROPIC) {
					// Anthropic has a different way of caching
					part.usage.promptTokens = part.usage.promptTokens ?? 0
					const cachedCreationTokens = part.providerMetadata?.["anthropic"]?.cacheCreationInputTokens
					const cachedPromptTokensRead = part.providerMetadata?.["anthropic"]?.cacheReadInputTokens
					if (typeof cachedPromptTokensRead === "number" && typeof cachedCreationTokens === "number") {
						cache_read_input_tokens = cachedPromptTokensRead ?? 0
						// total_tokens - cache_read_input_tokens = cache_creation_input_tokens
						cache_creation_input_tokens = cachedCreationTokens
					}
				}
				yield {
					code: 1,
					body: {
						anthropic: {
							content: [
								{
									type: "text",
									text,
								},
							],
							id: "1",
							role: "assistant",
							stop_reason: "stop_sequence",
							type: "message",
							stop_sequence: "</action>",
							model: modelId,
							usage: {
								input_tokens: inputTokens,
								output_tokens: part.usage.completionTokens,
								cache_creation_input_tokens,
								cache_read_input_tokens,
							},
						},
						internal: {
							inputTokens: inputTokens,
							outputTokens: part.usage.completionTokens,
							cacheCreationInputTokens: cache_creation_input_tokens ?? 0,
							cacheReadInputTokens: cache_read_input_tokens ?? 0,
						},
					},
				}
			}
			if (part.type === "error") {
				console.error(part.error)
				if (`${part.error}`.includes(`exceed context limit:`)) {
					throw new Error(
						"The context limit has been exceeded. Please try again with a shorter prompt. (context window exceeded)"
					)
				}
				// throw part.error
				// if (part.error instanceof Error) {
				// 	yield {
				// 		code: -1,
				// 		body: {
				// 			msg: part.error.message ?? "Unknown error",
				// 			status: 500,
				// 		},
				// 	}
				// }
			}
		}
	}

	getModel(): { id: string; info: ModelInfo } {
		return {
			id: this._options.model.id,
			info: this._options.model,
		}
	}
}
