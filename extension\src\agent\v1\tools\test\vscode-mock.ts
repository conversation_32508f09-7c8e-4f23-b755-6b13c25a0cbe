// Mock VSCode API for testing
export const window = {
  showErrorMessage: vi.fn(),
  showInformationMessage: vi.fn(),
  showWarningMessage: vi.fn(),
  createOutputChannel: vi.fn(() => ({
    appendLine: vi.fn(),
    show: vi.fn(),
    dispose: vi.fn()
  }))
}

export const workspace = {
  getConfiguration: vi.fn(() => ({
    get: vi.fn(),
    update: vi.fn()
  })),
  workspaceFolders: [],
  onDidChangeConfiguration: vi.fn()
}

export const commands = {
  registerCommand: vi.fn(),
  executeCommand: vi.fn()
}

export const Uri = {
  file: vi.fn((path: string) => ({ fsPath: path, path })),
  parse: vi.fn((uri: string) => ({ fsPath: uri, path: uri }))
}

export const ViewColumn = {
  One: 1,
  Two: 2,
  Three: 3
}

export const TextDocumentContentProvider = class {
  onDidChange = vi.fn()
  provideTextDocumentContent = vi.fn()
}

export const Disposable = class {
  static from = vi.fn()
  dispose = vi.fn()
}

export const EventEmitter = class {
  fire = vi.fn()
  event = vi.fn()
  dispose = vi.fn()
}

export const Range = class {
  constructor(public start: any, public end: any) {}
}

export const Position = class {
  constructor(public line: number, public character: number) {}
}

export const Selection = class extends Range {
  constructor(start: any, end: any) {
    super(start, end)
  }
}

export const TextEdit = {
  replace: vi.fn(),
  insert: vi.fn(),
  delete: vi.fn()
}

export const WorkspaceEdit = class {
  set = vi.fn()
  replace = vi.fn()
  insert = vi.fn()
  delete = vi.fn()
}

export const languages = {
  registerDocumentFormattingEditProvider: vi.fn(),
  registerDocumentRangeFormattingEditProvider: vi.fn(),
  registerOnTypeFormattingEditProvider: vi.fn()
}

export const env = {
  clipboard: {
    writeText: vi.fn(),
    readText: vi.fn()
  }
}

// Mock vi globally for this file
declare global {
  const vi: any
}

// If vi is not available, create a simple mock
if (typeof vi === 'undefined') {
  (global as any).vi = {
    fn: () => ({
      mockReturnValue: () => ({}),
      mockResolvedValue: () => ({}),
      mockRejectedValue: () => ({})
    })
  }
}