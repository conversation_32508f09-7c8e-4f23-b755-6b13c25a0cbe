import { PLANNER_SYSTEM_PROMPT } from "../../../prompts/agents/planner.prompt"
import { SUBTASK_SYSTEM_PROMPT } from "../../../prompts/agents/subtask.prompt"
import { CODER_SYSTEM_PROMPT } from "../../../prompts/agents/coder.prompt"
import { ANALYZER_SYSTEM_PROMPT } from "../../../prompts/agents/analyzer.prompt"
import { RESEARCHER_SYSTEM_PROMPT } from "../../../prompts/agents/researcher.prompt"

import { BaseAgentTool } from "../../base-agent.tool"
import { SpawnAgentToolParams } from "../../schema/agents/agent-spawner"
import { SubAgentExecutor } from "../../../sub-agent-executor"

export class SpawnAgentTool extends BaseAgentTool<SpawnAgentToolParams> {
	async execute() {
		const { input, ask, say } = this.params
		const { agentName, instructions, files } = input

		const filesList = files?.split(",").map((file) => file.trim()) || []

		// Generate task list first
		const taskList = await this.MainAgent.getStateManager().subAgentManager.generateTaskList(
			instructions,
			`Files: ${filesList.join(", ")}`
		)

		const { response, text, images } = await ask(
			"tool",
			{
				tool: {
					tool: "spawn_agent",
					agentName,
					instructions,
					files: filesList,
					ts: this.ts,
					approvalState: "pending",
				},
			},
			this.ts
		)
		if (response !== "yesButtonTapped") {
			await this.params.updateAsk(
				"tool",
				{
					tool: {
						tool: "spawn_agent",
						agentName,
						instructions,
						files: filesList,
						approvalState: "rejected",
						userFeedback: text,
						ts: this.ts,
					},
				},
				this.ts
			)
			if (response === "messageResponse") {
				await say("user_feedback", text ?? "The user denied this operation.", images)
				return this.toolResponse("feedback", text, images)
			}
			return this.toolResponse("error", "Sub-agent operation cancelled by user.")
		}
		await this.params.updateAsk(
			"tool",
			{
				tool: {
					tool: "spawn_agent",
					agentName,
					instructions,
					files: filesList,
					approvalState: "approved",
					userFeedback: text,
					ts: this.ts,
				},
			},
			this.ts
		)

		let systemPrompt = ""
		switch (agentName) {
			case "planner":
				systemPrompt = PLANNER_SYSTEM_PROMPT(this.MainAgent.getApiManager().getModelInfo()?.supportsImages)
				break
			case "sub_task":
				systemPrompt = SUBTASK_SYSTEM_PROMPT(this.MainAgent.getApiManager().getModelInfo()?.supportsImages)
				break
			case "coder":
				systemPrompt = CODER_SYSTEM_PROMPT(this.MainAgent.getApiManager().getModelInfo()?.supportsImages)
				break
			case "analyzer":
				systemPrompt = ANALYZER_SYSTEM_PROMPT(this.MainAgent.getApiManager().getModelInfo()?.supportsImages)
				break
			case "researcher":
				systemPrompt = RESEARCHER_SYSTEM_PROMPT(this.MainAgent.getApiManager().getModelInfo()?.supportsImages)
				break
		}

		// Create sub-agent with independent execution capability
		const subAgentState = {
			name: agentName,
			state: "RUNNING" as const,
			ts: this.ts,
			apiConversationHistory: [],
			historyErrors: {},
			systemPrompt,
			automaticReminders: `Task: ${instructions}\nFiles: ${filesList.join(", ")}`,
		}

		await this.MainAgent.getStateManager().subAgentManager.spawnSubAgent(this.ts, subAgentState)

		// Start independent execution in background
		this.startIndependentExecution(subAgentState, instructions, filesList)

		return this.toolResponse(
			"success",
			`Sub-agent "${agentName}" has been spawned and is running independently in the background.\n\nTask List generated and saved to .Ai/task-list.md\n\nInstructions: ${instructions}\nFiles: ${filesList.join(", ")}\n\nThe agent will work autonomously and generate a summary report when completed.`
		)
	}

	private async startIndependentExecution(
		agentState: any,
		instructions: string,
		files: string[]
	): Promise<void> {
		// This runs in the background without blocking the main agent
		setTimeout(async () => {
			try {
				// Create sub-agent executor with full tool access
				const executor = new SubAgentExecutor({
					subAgentId: agentState.ts.toString(),
					state: agentState,
					mainAgent: this.MainAgent,
					instructions,
					files
				})

				// Start independent execution
				await executor.startExecution()
				
				// Generate summary report when done
				const report = await this.MainAgent.getStateManager().subAgentManager.generateTaskSummaryReport(
					agentState.ts.toString(),
					{
						success: true,
						summary: `Successfully completed task: ${instructions}`,
						achievements: [
							"Analyzed task requirements",
							"Processed specified files",
							"Executed tools independently",
							"Generated implementation plan"
						],
						challenges: agentState.historyErrors ? Object.keys(agentState.historyErrors) : [],
						recommendations: ["Consider adding unit tests", "Review code for optimization"],
						filesModified: files,
					}
				)

				console.log(`Sub-agent ${agentState.ts} completed execution`)

			} catch (error) {
				console.error("Sub-agent execution failed:", error)
				await this.MainAgent.getStateManager().subAgentManager.updateAgentStatus(
					agentState.ts.toString(),
					"EXITED"
				)
			}
		}, 1000) // Start after 1 second
	}
}
