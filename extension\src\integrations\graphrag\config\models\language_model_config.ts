// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Language model configuration.
 */

import { encodingForModel } from 'js-tiktoken';
import { languageModelDefaults } from '../defaults.js';
import { AsyncType, AuthType, ModelType } from '../enums.js';
import {
  ApiKeyMissingError,
  AzureApiBaseMissingError,
  AzureApiVersionMissingError,
  AzureDeploymentNameMissingError,
  ConflictingSettingsError,
} from '../errors.js';

/**
 * Language model configuration.
 */
export interface LanguageModelConfig {
  /**
   * The API key to use for the LLM service.
   */
  apiKey?: string;

  /**
   * The authentication type.
   */
  authType: AuthType;

  /**
   * The type of LLM model to use.
   */
  type: ModelType | string;

  /**
   * The LLM model to use.
   */
  model: string;

  /**
   * The encoding model to use.
   */
  encodingModel: string;

  /**
   * The base URL for the LLM API.
   */
  apiBase?: string;

  /**
   * The version of the LLM API to use.
   */
  apiVersion?: string;

  /**
   * The deployment name to use for the LLM service.
   */
  deploymentName?: string;

  /**
   * The organization to use for the LLM service.
   */
  organization?: string;

  /**
   * The proxy to use for the LLM service.
   */
  proxy?: string;

  /**
   * Azure resource URI to use with managed identity for the llm connection.
   */
  audience?: string;

  /**
   * Whether the model supports JSON output mode.
   */
  modelSupportsJson?: boolean;

  /**
   * The request timeout to use.
   */
  requestTimeout: number;

  /**
   * The number of tokens per minute to use for the LLM service.
   */
  tokensPerMinute?: number | 'auto';

  /**
   * The number of requests per minute to use for the LLM service.
   */
  requestsPerMinute?: number | 'auto';

  /**
   * The retry strategy to use for the LLM service.
   */
  retryStrategy: string;

  /**
   * The maximum number of retries to use for the LLM service.
   */
  maxRetries: number;

  /**
   * The maximum retry wait to use for the LLM service.
   */
  maxRetryWait: number;

  /**
   * Whether to use concurrent requests for the LLM service.
   */
  concurrentRequests: number;

  /**
   * The async mode to use.
   */
  asyncMode: AsyncType;

  /**
   * Static responses to use in mock mode.
   */
  responses?: (string | Record<string, any>)[];

  /**
   * The maximum number of tokens to generate.
   */
  maxTokens?: number;

  /**
   * The temperature to use for token generation.
   */
  temperature: number;

  /**
   * The maximum number of tokens to consume. This includes reasoning tokens for the o* reasoning models.
   */
  maxCompletionTokens?: number;

  /**
   * Level of effort OpenAI reasoning models should expend. Supported options are 'low', 'medium', 'high'; and OAI defaults to 'medium'.
   */
  reasoningEffort?: string;

  /**
   * The top-p value to use for token generation.
   */
  topP: number;

  /**
   * The number of completions to generate.
   */
  n: number;

  /**
   * The frequency penalty to use for token generation.
   */
  frequencyPenalty: number;

  /**
   * The presence penalty to use for token generation.
   */
  presencePenalty: number;
}

/**
 * Create a LanguageModelConfig with default values.
 */
export function createLanguageModelConfig(config: Partial<LanguageModelConfig> & { type: ModelType | string; model: string }): LanguageModelConfig {
  const result: LanguageModelConfig = {
    apiKey: config.apiKey ?? languageModelDefaults.apiKey,
    authType: config.authType ?? languageModelDefaults.authType,
    type: config.type,
    model: config.model,
    encodingModel: config.encodingModel ?? languageModelDefaults.encodingModel,
    apiBase: config.apiBase ?? languageModelDefaults.apiBase,
    apiVersion: config.apiVersion ?? languageModelDefaults.apiVersion,
    deploymentName: config.deploymentName ?? languageModelDefaults.deploymentName,
    organization: config.organization ?? languageModelDefaults.organization,
    proxy: config.proxy ?? languageModelDefaults.proxy,
    audience: config.audience ?? languageModelDefaults.audience,
    modelSupportsJson: config.modelSupportsJson ?? languageModelDefaults.modelSupportsJson,
    requestTimeout: config.requestTimeout ?? languageModelDefaults.requestTimeout,
    tokensPerMinute: config.tokensPerMinute ?? languageModelDefaults.tokensPerMinute,
    requestsPerMinute: config.requestsPerMinute ?? languageModelDefaults.requestsPerMinute,
    retryStrategy: config.retryStrategy ?? languageModelDefaults.retryStrategy,
    maxRetries: config.maxRetries ?? languageModelDefaults.maxRetries,
    maxRetryWait: config.maxRetryWait ?? languageModelDefaults.maxRetryWait,
    concurrentRequests: config.concurrentRequests ?? languageModelDefaults.concurrentRequests,
    asyncMode: config.asyncMode ?? languageModelDefaults.asyncMode,
    responses: config.responses ?? languageModelDefaults.responses,
    maxTokens: config.maxTokens ?? languageModelDefaults.maxTokens,
    temperature: config.temperature ?? languageModelDefaults.temperature,
    maxCompletionTokens: config.maxCompletionTokens ?? languageModelDefaults.maxCompletionTokens,
    reasoningEffort: config.reasoningEffort ?? languageModelDefaults.reasoningEffort,
    topP: config.topP ?? languageModelDefaults.topP,
    n: config.n ?? languageModelDefaults.n,
    frequencyPenalty: config.frequencyPenalty ?? languageModelDefaults.frequencyPenalty,
    presencePenalty: config.presencePenalty ?? languageModelDefaults.presencePenalty,
  };

  // Set encoding model if empty
  if (!result.encodingModel || result.encodingModel.trim() === '') {
    try {
      result.encodingModel = encodingForModel(result.model).name;
    } catch {
      result.encodingModel = 'cl100k_base'; // fallback
    }
  }

  return result;
}

/**
 * Validate the API key.
 */
export function validateApiKey(config: LanguageModelConfig): void {
  if (config.authType === AuthType.APIKey && (!config.apiKey || config.apiKey.trim() === '')) {
    throw new ApiKeyMissingError(config.type, config.authType);
  }

  if (config.authType === AuthType.AzureManagedIdentity && config.apiKey && config.apiKey.trim() !== '') {
    throw new ConflictingSettingsError(
      'API Key should not be provided when using Azure Managed Identity. Please rerun `graphrag init` and remove the api_key when using Azure Managed Identity.'
    );
  }
}

/**
 * Validate the authentication type.
 */
export function validateAuthType(config: LanguageModelConfig): void {
  if (
    config.authType === AuthType.AzureManagedIdentity &&
    (config.type === ModelType.OpenAIChat || config.type === ModelType.OpenAIEmbedding)
  ) {
    throw new ConflictingSettingsError(
      `auth_type of azure_managed_identity is not supported for model type ${config.type}. Please rerun \`graphrag init\` and set the auth_type to api_key.`
    );
  }
}

/**
 * Validate the API base.
 */
export function validateApiBase(config: LanguageModelConfig): void {
  if (
    (config.type === ModelType.AzureOpenAIChat || config.type === ModelType.AzureOpenAIEmbedding) &&
    (!config.apiBase || config.apiBase.trim() === '')
  ) {
    throw new AzureApiBaseMissingError(config.type);
  }
}

/**
 * Validate the API version.
 */
export function validateApiVersion(config: LanguageModelConfig): void {
  if (
    (config.type === ModelType.AzureOpenAIChat || config.type === ModelType.AzureOpenAIEmbedding) &&
    (!config.apiVersion || config.apiVersion.trim() === '')
  ) {
    throw new AzureApiVersionMissingError(config.type);
  }
}

/**
 * Validate the deployment name.
 */
export function validateDeploymentName(config: LanguageModelConfig): void {
  if (
    (config.type === ModelType.AzureOpenAIChat || config.type === ModelType.AzureOpenAIEmbedding) &&
    (!config.deploymentName || config.deploymentName.trim() === '')
  ) {
    throw new AzureDeploymentNameMissingError(config.type);
  }
}

/**
 * Validate tokens per minute.
 */
export function validateTokensPerMinute(config: LanguageModelConfig): void {
  if (typeof config.tokensPerMinute === 'number' && config.tokensPerMinute < 1) {
    throw new Error(
      `Tokens per minute must be a non zero positive number, 'auto' or null. Suggested value: ${languageModelDefaults.tokensPerMinute}.`
    );
  }
}

/**
 * Validate requests per minute.
 */
export function validateRequestsPerMinute(config: LanguageModelConfig): void {
  if (typeof config.requestsPerMinute === 'number' && config.requestsPerMinute < 1) {
    throw new Error(
      `Requests per minute must be a non zero positive number, 'auto' or null. Suggested value: ${languageModelDefaults.requestsPerMinute}.`
    );
  }
}

/**
 * Validate max retries.
 */
export function validateMaxRetries(config: LanguageModelConfig): void {
  if (config.maxRetries < 1) {
    throw new Error(
      `Maximum retries must be greater than or equal to 1. Suggested value: ${languageModelDefaults.maxRetries}.`
    );
  }
}

/**
 * Validate the entire language model configuration.
 */
export function validateLanguageModelConfig(config: LanguageModelConfig): void {
  validateAuthType(config);
  validateApiKey(config);
  validateTokensPerMinute(config);
  validateRequestsPerMinute(config);
  validateMaxRetries(config);
  validateApiBase(config);
  validateApiVersion(config);
  validateDeploymentName(config);
}
