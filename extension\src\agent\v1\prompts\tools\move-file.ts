import { ToolPromptSchema } from "../utils/utils"

export const moveFilePrompt: ToolPromptSchema = {
	name: "move_file",
	description:
		"Move a file or directory from one location to another. This tool can relocate files within the project structure, helping with code organization and refactoring.",
	parameters: {
		source: {
			type: "string",
			description: "Path to the source file or directory to move. Can be relative or absolute path.",
			required: true,
		},
		destination: {
			type: "string",
			description: "Path to the destination location. Can be a directory path or full file path with new name.",
			required: true,
		},
		overwrite: {
			type: "boolean",
			description: "Whether to overwrite the destination if it already exists (default: false).",
			required: false,
		},
	},
	capabilities: [
		"Move files and directories",
		"Relocate code files for better organization",
		"Support for overwriting existing files",
		"Handle both relative and absolute paths",
	],
	examples: [
		{
			description: "Move a file to a different directory",
			output: `<move_file>
<source>src/utils/helper.ts</source>
<destination>src/lib/helper.ts</destination>
</move_file>`,
		},
		{
			description: "Move a file and rename it",
			output: `<move_file>
<source>components/Button.tsx</source>
<destination>components/ui/CustomButton.tsx</destination>
</move_file>`,
		},
		{
			description: "Move with overwrite permission",
			output: `<move_file>
<source>temp/config.json</source>
<destination>src/config.json</destination>
<overwrite>true</overwrite>
</move_file>`,
		},
	],
}