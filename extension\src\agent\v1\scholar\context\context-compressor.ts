/**
 * Context Compressor - 智能上下文压缩
 * 
 * 实现类似人类记忆的压缩机制：
 * 1. 保留核心信息
 * 2. 抽象化具体细节
 * 3. 建立关联关系
 */

import { LearningContext, CompressionResult, Pattern, Knowledge } from '../types'

export class ContextCompressor {
	private readonly MAX_INSIGHT_LENGTH = 200
	private readonly MIN_PATTERN_FREQUENCY = 2

	/**
	 * 压缩上下文为核心洞察
	 */
	async compressContext(
		context: LearningContext, 
		keyInformation: any
	): Promise<Omit<CompressionResult, 'confidence'>> {
		console.log(`[ContextCompressor] Compressing context: ${context.id}`)

		// Step 1: 生成核心洞察
		const coreInsights = this.generateCoreInsights(keyInformation)
		
		// Step 2: 识别关键模式
		const keyPatterns = this.identifyPatterns(keyInformation)
		
		// Step 3: 提取可执行知识
		const actionableKnowledge = this.extractActionableKnowledge(keyInformation, context)
		
		// Step 4: 计算压缩比
		const originalLength = context.originalContext.length
		const compressedLength = coreInsights.join(' ').length
		const compressionRatio = originalLength > 0 ? compressedLength / originalLength : 0

		return {
			coreInsights,
			keyPatterns,
			actionableKnowledge,
			compressionRatio
		}
	}

	/**
	 * 生成核心洞察
	 */
	private generateCoreInsights(keyInfo: any): string[] {
		const insights: string[] = []

		// From main topics
		if (keyInfo.mainTopics?.length > 0) {
			const topicInsight = this.synthesizeTopics(keyInfo.mainTopics)
			if (topicInsight) {
				insights.push(topicInsight)
			}
		}

		// From problem-solutions
		if (keyInfo.problemSolutions?.length > 0) {
			for (const ps of keyInfo.problemSolutions) {
				const insight = this.compressProblemSolution(ps.problem, ps.solution)
				if (insight) {
					insights.push(insight)
				}
			}
		}

		// From action sequences
		if (keyInfo.actionSequences?.length > 0) {
			const sequenceInsight = this.compressActionSequences(keyInfo.actionSequences)
			if (sequenceInsight) {
				insights.push(sequenceInsight)
			}
		}

		// From decision points
		if (keyInfo.decisionPoints?.length > 0) {
			const decisionInsight = this.compressDecisions(keyInfo.decisionPoints)
			if (decisionInsight) {
				insights.push(decisionInsight)
			}
		}

		return insights.slice(0, 5) // Top 5 insights
	}

	/**
	 * 综合主题信息
	 */
	private synthesizeTopics(topics: string[]): string | null {
		if (topics.length === 0) return null

		// Find common themes
		const commonWords = this.findCommonWords(topics)
		const mainTheme = commonWords[0] || 'development'

		// Create synthesis
		const synthesis = `Key development focus: ${mainTheme} involving ${topics.length} related activities`
		
		return synthesis.length <= this.MAX_INSIGHT_LENGTH ? synthesis : null
	}

	/**
	 * 压缩问题解决方案
	 */
	private compressProblemSolution(problem: string, solution: string): string | null {
		// Extract key terms
		const problemKey = this.extractKeyTerms(problem)[0] || 'issue'
		const solutionKey = this.extractKeyTerms(solution)[0] || 'fix'

		const compressed = `${problemKey} resolved by ${solutionKey}`
		
		return compressed.length <= this.MAX_INSIGHT_LENGTH ? compressed : null
	}

	/**
	 * 压缩行动序列
	 */
	private compressActionSequences(sequences: string[]): string | null {
		if (sequences.length === 0) return null

		// Extract workflow pattern
		const actions = sequences.flatMap(seq => 
			seq.split('→').map(part => this.extractKeyTerms(part.trim())[0])
		).filter(Boolean)

		if (actions.length >= 2) {
			const workflow = actions.slice(0, 3).join(' → ')
			return `Workflow pattern: ${workflow}`
		}

		return null
	}

	/**
	 * 压缩决策信息
	 */
	private compressDecisions(decisions: string[]): string | null {
		if (decisions.length === 0) return null

		// Extract decision criteria
		const criteria = decisions.map(decision => 
			this.extractKeyTerms(decision).slice(0, 2)
		).flat()

		const uniqueCriteria = [...new Set(criteria)].slice(0, 3)
		
		if (uniqueCriteria.length > 0) {
			return `Decision factors: ${uniqueCriteria.join(', ')}`
		}

		return null
	}

	/**
	 * 识别关键模式
	 */
	private identifyPatterns(keyInfo: any): Pattern[] {
		const patterns: Pattern[] = []

		// Solution patterns
		if (keyInfo.problemSolutions?.length > 0) {
			const solutionPattern = this.createSolutionPattern(keyInfo.problemSolutions)
			if (solutionPattern) {
				patterns.push(solutionPattern)
			}
		}

		// Workflow patterns
		if (keyInfo.actionSequences?.length > 0) {
			const workflowPattern = this.createWorkflowPattern(keyInfo.actionSequences)
			if (workflowPattern) {
				patterns.push(workflowPattern)
			}
		}

		// Code patterns
		if (keyInfo.codePatterns?.length > 0) {
			const codePattern = this.createCodePattern(keyInfo.codePatterns)
			if (codePattern) {
				patterns.push(codePattern)
			}
		}

		return patterns
	}

	/**
	 * 创建解决方案模式
	 */
	private createSolutionPattern(problemSolutions: any[]): Pattern | null {
		if (problemSolutions.length === 0) return null

		const commonSolutions = this.findCommonWords(
			problemSolutions.map(ps => ps.solution)
		)

		return {
			id: `solution-${Date.now()}`,
			type: 'solution',
			description: `Common solution approach: ${commonSolutions[0] || 'systematic fix'}`,
			context: problemSolutions[0].problem,
			frequency: problemSolutions.length,
			effectiveness: 0.8 // Default effectiveness
		}
	}

	/**
	 * 创建工作流模式
	 */
	private createWorkflowPattern(sequences: string[]): Pattern | null {
		if (sequences.length === 0) return null

		return {
			id: `workflow-${Date.now()}`,
			type: 'workflow',
			description: `Development workflow with ${sequences.length} steps`,
			context: sequences[0],
			frequency: sequences.length,
			effectiveness: 0.7
		}
	}

	/**
	 * 创建代码模式
	 */
	private createCodePattern(codePatterns: string[]): Pattern | null {
		if (codePatterns.length === 0) return null

		return {
			id: `code-${Date.now()}`,
			type: 'best-practice',
			description: codePatterns[0],
			context: 'Code implementation',
			frequency: codePatterns.length,
			effectiveness: 0.9
		}
	}

	/**
	 * 提取可执行知识
	 */
	private extractActionableKnowledge(keyInfo: any, context: LearningContext): Knowledge[] {
		const knowledge: Knowledge[] = []

		// From problem solutions
		if (keyInfo.problemSolutions?.length > 0) {
			for (const ps of keyInfo.problemSolutions) {
				knowledge.push({
					id: `knowledge-${Date.now()}-${Math.random()}`,
					title: `Solution: ${this.extractKeyTerms(ps.problem)[0] || 'Problem'}`,
					content: `Problem: ${ps.problem}\nSolution: ${ps.solution}`,
					category: 'solutions',
					tags: this.extractKeyTerms(ps.solution),
					importance: 0.8,
					applicability: ['debugging', 'problem-solving'],
					created: new Date(),
					useCount: 0
				})
			}
		}

		// From code patterns
		if (keyInfo.codePatterns?.length > 0) {
			for (const pattern of keyInfo.codePatterns) {
				knowledge.push({
					id: `knowledge-${Date.now()}-${Math.random()}`,
					title: 'Code Pattern',
					content: pattern,
					category: 'patterns',
					tags: ['code', 'implementation'],
					importance: 0.7,
					applicability: ['coding', 'implementation'],
					created: new Date(),
					useCount: 0
				})
			}
		}

		return knowledge.slice(0, 3) // Top 3 actionable knowledge items
	}

	/**
	 * 查找常见词汇
	 */
	private findCommonWords(texts: string[]): string[] {
		const wordCount = new Map<string, number>()
		const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'])

		for (const text of texts) {
			const words = text.toLowerCase()
				.replace(/[^\w\s]/g, ' ')
				.split(/\s+/)
				.filter(word => word.length > 2 && !stopWords.has(word))

			for (const word of words) {
				wordCount.set(word, (wordCount.get(word) || 0) + 1)
			}
		}

		return Array.from(wordCount.entries())
			.filter(([_, count]) => count >= this.MIN_PATTERN_FREQUENCY)
			.sort((a, b) => b[1] - a[1])
			.map(([word, _]) => word)
			.slice(0, 5)
	}

	/**
	 * 提取关键术语
	 */
	private extractKeyTerms(text: string): string[] {
		const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'])
		
		return text.toLowerCase()
			.replace(/[^\w\s]/g, ' ')
			.split(/\s+/)
			.filter(word => word.length > 2 && !stopWords.has(word))
			.slice(0, 3)
	}
}