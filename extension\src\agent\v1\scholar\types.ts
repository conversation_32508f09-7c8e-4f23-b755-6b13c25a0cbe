/**
 * Scholar Agent Type Definitions
 */

// Core Learning Types
export interface LearningContext {
	id: string
	originalContext: string
	compressedContext?: string
	taskContext: string
	messages: any[]
	timestamp: number
	metadata: Record<string, any>
}

export interface CompressionResult {
	coreInsights: string[]
	keyPatterns: Pattern[]
	actionableKnowledge: Knowledge[]
	compressionRatio: number
	confidence: number
}

export interface Pattern {
	id: string
	type: 'solution' | 'workflow' | 'best-practice' | 'anti-pattern'
	description: string
	context: string
	frequency: number
	effectiveness: number
}

export interface Knowledge {
	id: string
	title: string
	content: string
	category: KnowledgeCategory
	tags: string[]
	importance: number
	applicability: string[]
	created: Date
	lastUsed?: Date
	useCount: number
}

export type KnowledgeCategory = 
	| 'patterns' 
	| 'solutions' 
	| 'best-practices' 
	| 'workflows' 
	| 'architecture' 
	| 'debugging' 
	| 'general'

// Knowledge Graph Types
export interface Entity {
	id: string
	type: EntityType
	name: string
	description: string
	properties: Record<string, any>
	embedding?: number[]
}

export type EntityType = 
	| 'concept' 
	| 'tool' 
	| 'technique' 
	| 'problem' 
	| 'solution' 
	| 'pattern'

export interface Relation {
	id: string
	sourceId: string
	targetId: string
	type: RelationType
	strength: number
	properties: Record<string, any>
}

export type RelationType = 
	| 'uses' 
	| 'solves' 
	| 'implements' 
	| 'depends-on' 
	| 'similar-to' 
	| 'part-of'

export interface KnowledgeGraph {
	entities: Entity[]
	relations: Relation[]
	clusters: Cluster[]
	importanceScores: Map<string, number>
}

export interface Cluster {
	id: string
	name: string
	entities: string[]
	centroid: number[]
	coherence: number
}

// RAG Types
export interface RAGQuery {
	query: string
	context?: string
	retrievalStrategy: 'semantic' | 'graph' | 'hybrid'
	maxResults: number
	filters?: Record<string, any>
}

export interface RAGResult {
	results: RetrievedKnowledge[]
	totalResults: number
	queryTime: number
	confidence: number
}

export interface RetrievedKnowledge {
	knowledge: Knowledge
	similarity: number
	relevanceScore: number
	contextMatch: number
}

// Storage Types
export interface StorageConfig {
	fileStorage: {
		basePath: string
		maxFileSize: number
	}
	vectorStorage: {
		provider: 'faiss' | 'chroma' | 'local'
		dimensions: number
		indexType: string
	}
	graphStorage: {
		provider: 'memory' | 'neo4j' | 'networkx'
		connectionString?: string
	}
}

// Learning Metrics
export interface LearningMetrics {
	compressionRatio: number
	retrievalAccuracy: number
	knowledgeReuseRate: number
	averageResponseTime: number
	storageEfficiency: number
	learningVelocity: number
}

// Configuration
export interface ScholarConfig {
	learning: {
		compressionThreshold: number
		maxContextLength: number
		minImportanceScore: number
	}
	storage: StorageConfig
	rag: {
		embeddingModel: string
		maxRetrievalResults: number
		similarityThreshold: number
	}
	performance: {
		maxMemoryUsage: number
		cacheSize: number
		indexUpdateInterval: number
	}
}