// providers/anthropic.ts
import { ProviderConfig } from "../types"
import { DEFAULT_BASE_URLS, PROVIDER_IDS, PROVIDER_NAMES } from "../constants"

export const anthropicConfig: ProviderConfig = {
	id: PROVIDER_IDS.ANTHROPIC,
	name: PROVIDER_NAMES[PROVIDER_IDS.ANTHROPIC],
	baseUrl: DEFAULT_BASE_URLS[PROVIDER_IDS.ANTHROPIC],
	models: [
		{
			id: "claude-sonnet-4-20250514",
			maxTokens: 8192,
			contextWindow: 200_000,
			supportsImages: true,
			supportsPromptCache: true,
			inputPrice: 3.0,
			outputPrice: 15.0,
			cacheWritesPrice: 3.75,
			cacheReadsPrice: 0.3,
			name: "Claude Sonnet 4",
			provider: PROVIDER_IDS.ANTHROPIC,
		},
		{
			id: "claude-opus-4-20250514",
			maxTokens: 8192,
			contextWindow: 200_000,
			supportsImages: true,
			supportsPromptCache: true,
			inputPrice: 15.0,
			outputPrice: 75.0,
			cacheWritesPrice: 18.75,
			cacheReadsPrice: 1.5,
			name: "Claude Opus 4",
			provider: PROVIDER_IDS.ANTHROPIC,
		},
		{
			id: "claude-3-7-sonnet-20250219",
			maxTokens: 8192,
			contextWindow: 200_000,
			supportsImages: true,
			isThinkingModel: true,
			supportsPromptCache: true,
			inputPrice: 3.0,
			outputPrice: 15.0,
			cacheWritesPrice: 3.75,
			cacheReadsPrice: 0.3,
			name: "Claude 3.7 Sonnet - Anthropic Direct",
			provider: PROVIDER_IDS.ANTHROPIC,
		},
	],
	requiredFields: ["apiKey"],
}
