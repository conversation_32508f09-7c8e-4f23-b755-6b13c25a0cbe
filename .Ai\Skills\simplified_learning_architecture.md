# 简化学习系统架构

## 核心流程图

```mermaid
flowchart LR
    A[🤖 大模型] --> B[📋 learning.ts]
    B --> C[🔍 检索聊天上下文]
    C --> D[🧠 思考分析]
    D --> E[✨ 总结成功经验]
    E --> F[⚙️ 获取技能流程]
    F --> G[📁 创建.Ai\Skills]
    G --> H[💾 存储技能]
    H --> I[🏷️ 按名称分类]
    
    J[❓ 复杂情况] --> K[🔎 检索技能库]
    K --> L[📤 返回技能]
    L --> M[⚡ 应用到代码]
    M --> N[📊 获取反馈]
    N --> O[🔄 更新技能]
    O --> H
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style K fill:#fce4ec
```

## 数据流向图

```mermaid
graph TD
    subgraph "学习阶段"
        A1[成功解决问题] --> A2[触发学习]
        A2 --> A3[分析上下文]
        A3 --> A4[提取技能]
        A4 --> A5[保存到文件]
    end
    
    subgraph "应用阶段"
        B1[遇到新问题] --> B2[搜索技能]
        B2 --> B3[匹配最佳技能]
        B3 --> B4[执行技能]
        B4 --> B5[收集反馈]
    end
    
    subgraph "优化阶段"
        C1[分析反馈] --> C2[识别改进点]
        C2 --> C3[更新技能]
        C3 --> C4[验证效果]
    end
    
    A5 --> B2
    B5 --> C1
    C4 --> B2
    
    style A1,A2,A3,A4,A5 fill:#e8f5e8
    style B1,B2,B3,B4,B5 fill:#e1f5fe
    style C1,C2,C3,C4 fill:#fff3e0
```

## 技能生命周期

```mermaid
stateDiagram-v2
    [*] --> 创建: 成功经验触发
    创建 --> 存储: 技能数据生成
    存储 --> 分类: 保存到.Ai/Skills
    分类 --> 待用: 按类型组织
    
    待用 --> 检索: 问题匹配
    检索 --> 应用: 找到合适技能
    应用 --> 反馈: 执行完成
    反馈 --> 评估: 收集结果
    
    评估 --> 优化: 需要改进
    评估 --> 待用: 表现良好
    优化 --> 更新: 改进技能
    更新 --> 待用: 更新完成
    
    待用 --> 归档: 长期未使用
    归档 --> [*]: 清理过期技能
```

## 核心组件关系

```mermaid
graph TB
    subgraph "learning.ts 核心引擎"
        LT[learning.ts]
        LT --> LA[analyze 分析器]
        LT --> LC[create_skill 创建器]
        LT --> LS[search_skill 搜索器]
        LT --> LU[update_skill 更新器]
        LT --> LF[文件管理器]
    end
    
    subgraph ".Ai/Skills 存储系统"
        CS[core_skills/]
        DS[domain_skills/]
        WP[workflow_patterns/]
        TC[tool_combinations/]
        ST[skill_templates/]
    end
    
    subgraph "智能化功能"
        AI[auto_evolve 自动进化]
        FI[form_intuition 直觉形成]
        CO[compose_skills 技能组合]
    end
    
    LA --> CS
    LC --> DS
    LS --> WP
    LU --> TC
    LF --> ST
    
    CS --> AI
    DS --> FI
    WP --> CO
    
    style LT fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    style CS,DS,WP,TC,ST fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style AI,FI,CO fill:#fff3e0,stroke:#e65100,stroke-width:2px
```

## 实际执行示例

### 示例1: 调试技能学习
```mermaid
sequenceDiagram
    participant U as 用户
    participant L as 大模型
    participant T as learning.ts
    participant F as 文件系统
    
    U->>L: 遇到调试问题
    L->>L: 成功解决问题
    L->>T: 调用analyze分析
    T->>T: 提取调试模式
    T->>T: 创建debugging_workflow技能
    T->>F: 保存到core_skills/debugging_workflow.json
    F->>T: 确认保存
    T->>L: 学习完成
    
    Note over U,F: 下次遇到类似问题
    U->>L: 新的调试问题
    L->>T: 搜索相关技能
    T->>F: 加载debugging_workflow.json
    F->>T: 返回技能数据
    T->>L: 提供调试步骤
    L->>U: 应用调试技能
```

### 示例2: 复杂问题技能组合
```mermaid
graph LR
    A[复杂开发任务] --> B[技能分解]
    B --> C[前端技能]
    B --> D[后端技能]
    B --> E[测试技能]
    
    C --> F[技能组合执行]
    D --> F
    E --> F
    
    F --> G[监控执行结果]
    G --> H[收集各技能反馈]
    H --> I[更新组合策略]
    I --> J[保存优化后的组合技能]
    
    style A fill:#fce4ec
    style B fill:#e8f5e8
    style C,D,E fill:#e1f5fe
    style F fill:#fff3e0
    style G,H,I,J fill:#f3e5f5
```

## 技能质量保证

```mermaid
graph TD
    A[技能创建] --> B[初始质量检查]
    B --> C{通过检查?}
    C -->|是| D[保存技能]
    C -->|否| E[修正技能]
    E --> B
    
    D --> F[技能使用]
    F --> G[性能监控]
    G --> H[成功率统计]
    H --> I{达到阈值?}
    I -->|是| J[标记为优质技能]
    I -->|否| K[触发优化]
    
    K --> L[分析失败案例]
    L --> M[增量更新]
    M --> N[A/B测试]
    N --> O{效果改善?}
    O -->|是| P[应用更新]
    O -->|否| Q[回滚版本]
    
    P --> F
    Q --> F
    
    style A,D fill:#e8f5e8
    style B,C,I,O fill:#fff3e0
    style F,G,H fill:#e1f5fe
    style J fill:#c8e6c9
    style K,L,M,N fill:#fce4ec
```

## 关键特性总结

### 🎯 核心价值
- **自动学习**: 从成功经验中自动提取可重用技能
- **智能应用**: 遇到问题时自动匹配和应用相关技能
- **持续优化**: 基于反馈不断改进技能质量

### 🔧 技术特点
- **模块化设计**: learning.ts作为核心引擎，功能清晰分离
- **文件系统存储**: 使用.Ai/Skills目录结构化存储技能
- **多格式支持**: JSON、Markdown、YAML等多种存储格式

### 📈 智能化程度
- **模式识别**: 自动识别成功的问题解决模式
- **技能组合**: 智能组合多个技能解决复杂问题
- **直觉形成**: 高频技能转化为直觉反应

### 🛡️ 质量保证
- **成功率跟踪**: 监控每个技能的执行成功率
- **自动回滚**: 质量下降时自动回滚到稳定版本
- **持续改进**: 基于使用反馈持续优化技能

这个简化的架构图突出了学习系统的核心流程和关键特性，更容易理解和实施。