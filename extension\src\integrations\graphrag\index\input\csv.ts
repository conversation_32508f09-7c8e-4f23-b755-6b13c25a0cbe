/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing load method definition.
 */

import { DataFrame } from '../../data-model/types';
import { InputConfig } from '../../config/models/input-config';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { loadFiles, processDataColumns } from './util';

const logger = console;

/**
 * Load csv inputs from a directory.
 * @param config - Input configuration
 * @param storage - Pipeline storage instance
 * @returns Promise resolving to DataFrame with CSV data
 */
export async function loadCsv(
    config: InputConfig,
    storage: PipelineStorage,
): Promise<DataFrame> {
    logger.info(`Loading csv files from ${config.storage.baseDir}`);

    async function loadFile(path: string, group?: Record<string, any>): Promise<DataFrame> {
        const groupData = group || {};
        
        // Get file content as bytes and parse CSV
        const buffer = await storage.get(path, { asBytes: true });
        const csvText = new TextDecoder(config.encoding || 'utf-8').decode(buffer);
        
        // Simple CSV parsing (for production, consider using a proper CSV library)
        const lines = csvText.trim().split('\n');
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        
        const data = lines.slice(1).map(line => {
            const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
            const row: Record<string, any> = {};
            
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            
            // Add group data
            const additionalKeys = Object.keys(groupData);
            additionalKeys.forEach(key => {
                row[key] = groupData[key];
            });
            
            return row;
        });

        let result: DataFrame = {
            columns: [...headers, ...Object.keys(groupData)],
            data: data
        };

        result = processDataColumns(result, config, path);

        const creationDate = await storage.getCreationDate(path);
        result.data = result.data.map(row => ({
            ...row,
            creation_date: creationDate
        }));

        return result;
    }

    return await loadFiles(loadFile, config, storage);
}