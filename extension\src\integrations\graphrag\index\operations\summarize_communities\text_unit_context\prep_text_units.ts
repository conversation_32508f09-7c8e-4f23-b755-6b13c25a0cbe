// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Prepare text units for community reports.
 */

import { DataFrame } from '../../../../data_model/types.js';
import * as schemas from '../../../../data_model/schemas.js';

/**
 * Calculate text unit degree and concatenate text unit details.
 * 
 * Returns: dataframe with columns [COMMUNITY_ID, TEXT_UNIT_ID, ALL_DETAILS]
 */
export function prepTextUnits(
  textUnitDf: DataFrame,
  nodeDf: DataFrame
): DataFrame {
  // Drop the id column from node_df
  const nodeWithoutId = nodeDf.drop(['id']);
  
  // Explode text unit IDs and rename
  const nodeToTextIds = nodeWithoutId
    .explode(schemas.TEXT_UNIT_IDS)
    .rename({ [schemas.TEXT_UNIT_IDS]: schemas.ID })
    .select([
      schemas.TITLE,
      schemas.COMMUNITY_ID,
      schemas.NODE_DEGREE,
      schemas.ID
    ]);

  // Calculate text unit degrees by grouping and summing node degrees
  const textUnitDegrees = nodeToTextIds
    .groupBy([schemas.COMMUNITY_ID, schemas.ID])
    .agg({ [schemas.NODE_DEGREE]: 'sum' });

  // Merge with text unit dataframe
  const resultDf = textUnitDf.merge(textUnitDegrees, schemas.ID, 'left');

  // Create ALL_DETAILS column
  const finalDf = resultDf.withColumn(
    schemas.ALL_DETAILS,
    resultDf.toRecords().map(row => ({
      [schemas.SHORT_ID]: row[schemas.SHORT_ID],
      [schemas.TEXT]: row[schemas.TEXT],
      [schemas.ENTITY_DEGREE]: row[schemas.NODE_DEGREE],
    }))
  );

  return finalDf.select([schemas.COMMUNITY_ID, schemas.ID, schemas.ALL_DETAILS]);
}
