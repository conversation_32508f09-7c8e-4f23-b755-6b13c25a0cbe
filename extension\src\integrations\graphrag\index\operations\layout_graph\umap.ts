/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run and _create_node_position methods definitions.
 */

import { Graph } from '../../utils/graphs';
import { NodeEmbeddings } from '../embed-graph/typing';
import { GraphLayout, NodePosition } from './typing';
import { ErrorHandlerFn } from '../../typing/error-handler';

const logger = console;

/**
 * Run UMAP layout algorithm.
 */
export function run(
    graph: Graph,
    embeddings: NodeEmbeddings,
    onError: ErrorHandlerFn
): GraphLayout {
    const nodeClusters: number[] = [];
    const nodeSizes: number[] = [];

    const filteredEmbeddings = filterRawEmbeddings(embeddings);
    const nodes = Object.keys(filteredEmbeddings);
    const embeddingVectors = nodes.map(nodeId => filteredEmbeddings[nodeId]);

    for (const nodeId of nodes) {
        const node = graph.nodes.get(nodeId) || {};
        const cluster = node.cluster || node.community || -1;
        nodeClusters.push(cluster);
        const size = node.degree || node.size || 0;
        nodeSizes.push(size);
    }

    const additionalArgs: any = {};
    if (nodeClusters.length > 0) {
        additionalArgs.nodeCategories = nodeClusters;
    }
    if (nodeSizes.length > 0) {
        additionalArgs.nodeSizes = nodeSizes;
    }

    try {
        return computeUmapPositions(
            embeddingVectors,
            nodes,
            additionalArgs.nodeCategories,
            additionalArgs.nodeSizes
        );
    } catch (e) {
        const error = e instanceof Error ? e : new Error(String(e));
        logger.error('Error running UMAP', error);
        onError(error, error.stack || '', null);
        
        // UMAP may fail due to input sparseness or memory pressure.
        // For now, in these cases, we'll just return a layout with all nodes at (0, 0)
        const result: NodePosition[] = [];
        for (let i = 0; i < nodes.length; i++) {
            const cluster = nodeClusters.length > 0 ? nodeClusters[i] : 1;
            result.push({
                x: 0,
                y: 0,
                label: nodes[i],
                size: 0,
                cluster: String(cluster)
            });
        }
        return result;
    }
}

/**
 * Filter out null embeddings.
 */
function filterRawEmbeddings(embeddings: NodeEmbeddings): NodeEmbeddings {
    const filtered: NodeEmbeddings = {};
    for (const [nodeId, embedding] of Object.entries(embeddings)) {
        if (embedding !== null && embedding !== undefined) {
            filtered[nodeId] = embedding;
        }
    }
    return filtered;
}

/**
 * Project embedding vectors down to 2D/3D using UMAP.
 * Note: This is a simplified implementation. In production, you would use a proper UMAP library.
 */
function computeUmapPositions(
    embeddingVectors: number[][],
    nodeLabels: string[],
    nodeCategories?: number[],
    nodeSizes?: number[],
    minDist: number = 0.75,
    nNeighbors: number = 5,
    spread: number = 1,
    metric: string = "euclidean",
    nComponents: number = 2,
    randomState: number = 86
): NodePosition[] {
    // Simplified UMAP implementation - in reality you'd use a proper UMAP library
    console.warn('UMAP implementation is simplified. Consider using umap-js or similar library.');
    
    const embeddingPositionData: NodePosition[] = [];
    
    for (let index = 0; index < nodeLabels.length; index++) {
        const nodeName = nodeLabels[index];
        // Simple random positioning as placeholder
        const nodePoints = [
            Math.random() * 100 - 50, // x: -50 to 50
            Math.random() * 100 - 50  // y: -50 to 50
        ];
        
        const nodeCategory = nodeCategories ? nodeCategories[index] : 1;
        const nodeSize = nodeSizes ? nodeSizes[index] : 1;

        if (nComponents === 2) {
            embeddingPositionData.push({
                label: String(nodeName),
                x: nodePoints[0],
                y: nodePoints[1],
                cluster: String(Math.floor(nodeCategory)),
                size: Math.floor(nodeSize)
            });
        } else {
            embeddingPositionData.push({
                label: String(nodeName),
                x: nodePoints[0],
                y: nodePoints[1],
                z: Math.random() * 100 - 50, // z: -50 to 50
                cluster: String(Math.floor(nodeCategory)),
                size: Math.floor(nodeSize)
            });
        }
    }
    
    return embeddingPositionData;
}