// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing create_cache method definition.
 */

import { CacheType } from '../config/enums';
import { PipelineCache } from './pipeline-cache';
import { JsonPipelineCache } from './json-pipeline-cache';
import { InMemoryCache } from './memory-pipeline-cache';
import { NoopPipelineCache } from './noop-pipeline-cache';
import { FilePipelineStorage } from '../storage/file-pipeline-storage';
import { createBlobStorage } from '../storage/blob-pipeline-storage';
import { createCosmosdbStorage } from '../storage/cosmosdb-pipeline-storage';

/**
 * A factory class for cache implementations.
 * 
 * Includes a method for users to register a custom cache implementation.
 * 
 * Configuration arguments are passed to each cache implementation as kwargs (where possible)
 * for individual enforcement of required/optional arguments.
 */
export class CacheFactory {
    private static cacheTypes: Map<string, new (...args: any[]) => PipelineCache> = new Map();

    /**
     * Register a custom cache implementation.
     */
    static register(cacheType: string, cache: new (...args: any[]) => PipelineCache): void {
        this.cacheTypes.set(cacheType, cache);
    }

    /**
     * Create or get a cache from the provided type.
     */
    static createCache(
        cacheType: CacheType | string | null | undefined,
        rootDir: string,
        kwargs: Record<string, any>
    ): PipelineCache {
        if (!cacheType) {
            return new NoopPipelineCache();
        }

        switch (cacheType) {
            case CacheType.NONE:
                return new NoopPipelineCache();
            case CacheType.MEMORY:
                return new InMemoryCache();
            case CacheType.FILE:
                return new JsonPipelineCache(
                    new FilePipelineStorage({ rootDir }).child(kwargs.base_dir)
                );
            case CacheType.BLOB:
                return new JsonPipelineCache(createBlobStorage(kwargs));
            case CacheType.COSMOSDB:
                return new JsonPipelineCache(createCosmosdbStorage(kwargs));
            default:
                if (this.cacheTypes.has(cacheType)) {
                    const CacheClass = this.cacheTypes.get(cacheType)!;
                    return new CacheClass(kwargs);
                }
                const msg = `Unknown cache type: ${cacheType}`;
                throw new Error(msg);
        }
    }
}